/* Global Styles */
:root {
    --primary-color: #4361ee;
    --primary-hover: #3a56d4;
    --secondary-color: #4cc9f0;
    --accent-color: #f72585;
    --dark-color: #212529;
    --light-color: #f8f9fa;
    --gray-color: #6c757d;
    --success-color: #2ecc71;
    --sidebar-width: 250px;
    --topbar-height: 60px;
    --transition-speed: 0.3s;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
    --box-shadow-hover: 0 1rem 2rem rgba(0, 0, 0, 0.12);
    --card-border-radius: 16px;
    --btn-border-radius: 12px;
}

* {
    transition: all var(--transition-speed) ease;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--light-color);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

.btn {
    border-radius: 8px;
    padding: 0.6rem 1.5rem;
    font-weight: 500;
    box-shadow: 0 4px 6px rgba(50, 50, 93, 0.1);
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Landing Page */
.landing-page {
    background-color: #fff;
    overflow-x: hidden;
}

.hero-section {
    position: relative;
    padding: 160px 0 120px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow: hidden;
}

.hero-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
}

.shape-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(76, 201, 240, 0.1) 0%, rgba(67, 97, 238, 0.05) 100%);
    top: -100px;
    right: -100px;
}

.shape-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(247, 37, 133, 0.1) 0%, rgba(67, 97, 238, 0.05) 100%);
    bottom: -200px;
    left: -200px;
}

.shape-3 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.1) 0%, rgba(67, 97, 238, 0.05) 100%);
    top: 50%;
    right: 10%;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 3.5rem;
    background: linear-gradient(120deg, var(--primary-color), var(--accent-color));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--gray-color);
    margin-bottom: 2rem;
}

.hero-features {
    color: var(--gray-color);
}

.hero-image-wrapper {
    position: relative;
    z-index: 1;
}

.hero-image {
    animation: float 6s ease-in-out infinite;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.15));
}

.navbar {
    padding: 1rem 0;
    transition: all 0.4s ease;
}

.navbar.scrolled {
    padding: 0.5rem 0;
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color) !important;
}

.navbar-brand i {
    animation: pulse 2s infinite;
}

.nav-link {
    position: relative;
    margin: 0 0.5rem;
    font-weight: 500;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hero-section {
    padding: 160px 0 120px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(76, 201, 240, 0.1) 0%, rgba(67, 97, 238, 0.05) 100%);
    z-index: 1;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(247, 37, 133, 0.1) 0%, rgba(67, 97, 238, 0.05) 100%);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}
.hero-section h1 {
    font-weight: 700;
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease;
}

/* Mobile view - applies only on screens 768px and smaller */
@media (max-width: 768px) {
    .hero-section h1 {
        margin-top: 4.5rem;
    }
}
.hero-section p {
    margin-bottom: 2rem;
    animation: fadeInUp 1.2s ease;
}

.hero-section .btn {
    animation: fadeInUp 1.4s ease;
}

.hero-section img {
    animation: float 6s ease-in-out infinite;
    transform: translateY(0px);
}

.feature-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: transform 0.4s ease, box-shadow 0.4s ease;
    background: white;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.feature-card i {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    transition: transform 0.5s ease;
}

.feature-card:hover i {
    transform: scale(1.2) rotate(10deg);
}

/* Auth Pages */
.auth-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.auth-container {
    min-height: auto;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 1rem 0;
    margin-top: 1rem;
}

.auth-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    width: 100%;
    max-width: 480px;
    padding: 1.25rem;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.auth-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.auth-logo {
    width: 50px;
    height: 50px;
    margin: 0 auto 0.75rem;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: var(--gray-color);
    font-size: 0.95rem;
    margin-bottom: 1rem;
}

.btn-google {
    background: white;
    border: 1px solid #ddd;
    border-radius: var(--btn-border-radius);
    padding: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-google:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.google-icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
}

.features-list {
    background: var(--light-color);
    border-radius: var(--btn-border-radius);
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.feature-item {
    color: var(--dark-color);
    font-weight: 500;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.feature-item:last-child {
    margin-bottom: 0;
}

.signup-card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: transform 0.4s ease, box-shadow 0.4s ease;
}

.signup-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.feature-mini {
    font-size: 0.875rem;
    color: var(--gray-color);
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.feature-mini i {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

/* Dashboard Layout */
.dashboard-body {
    padding-top: var(--topbar-height);
}

#topbar {
    height: var(--topbar-height);
    z-index: 1030;
}

.sidebar {
    position: fixed;
    top: var(--topbar-height);
    left: 0;
    bottom: 0;
    width: var(--sidebar-width);
    background-color: #fff;
    border-right: 1px solid #dee2e6;
    transition: transform 0.3s ease;
    z-index: 1020;
}

.content {
    margin-left: var(--sidebar-width);
    padding: 2rem;
    transition: margin 0.3s ease;
}

.sb-sidenav-toggled .sidebar {
    transform: translateX(-100%);
}

.sb-sidenav-toggled .content {
    margin-left: 0;
}

/* Sidebar Navigation */
.sidebar .nav-link {
    color: #6c757d;
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    color: var(--primary-color);
    background-color: #f8f9fa;
}

.sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: #e7f1ff;
}

/* Cards */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #eee;
    padding: 1.25rem;
    font-weight: 600;
}

/* Pricing Cards */
.pricing-card {
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.4s ease, box-shadow 0.4s ease;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.pricing-card.border-primary {
    border: 2px solid var(--primary-color) !important;
    position: relative;
}

.pricing-card.border-primary::before {
    content: 'POPULAR';
    position: absolute;
    top: 0;
    right: 2rem;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.price-tag {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 1.5rem 0;
}

.price-tag .currency {
    font-size: 1.5rem;
    vertical-align: super;
    color: var(--gray-color);
}

.price-tag .period {
    font-size: 1rem;
    color: var(--gray-color);
    font-weight: 400;
}

/* Team Cards */
.team-card {
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.4s ease, box-shadow 0.4s ease;
}

.team-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
}

.team-card img {
    transition: transform 0.5s ease;
}

.team-card:hover img {
    transform: scale(1.1);
}

/* Logs Panel */
.logs-container {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
}

.logs-panel {
    height: 300px;
    overflow-y: auto;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 1rem;
}

.log-entry {
    padding: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.log-entry:last-child {
    border-bottom: none;
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-content {
    padding-left: 1rem;
    border-left: 2px solid #dee2e6;
}

/* Stats Cards */
.stat-card {
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .content {
        margin-left: 0;
    }

    .sb-sidenav-toggled .sidebar {
        transform: translateX(0);
    }
    
    .hero-section {
        padding: 120px 0 80px;
        text-align: center;
    }
    
    .hero-section .btn {
        margin-bottom: 0.5rem;
    }
    
    .hero-section img {
        margin-top: 3rem;
        max-width: 80%;
        margin-left: auto;
        margin-right: auto;
    }
}

@media (max-width: 768px) {
    .pricing-card, .team-card, .feature-card {
        margin-bottom: 1.5rem;
    }
    
    .pricing-card.border-primary::before {
        right: 1rem;
    }
    
    .navbar-collapse {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: var(--box-shadow);
        margin-top: 1rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }
    
    .navbar-nav .btn {
        margin: 0.5rem 1rem;
        display: block;
    }
}

@media (max-width: 576px) {
    .content {
        padding: 1rem;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .hero-section {
        padding: 100px 0 60px;
    }
    
    section {
        padding: 3rem 0 !important;
    }
    
    .card-body {
        padding: 1.25rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Features Section */
@media (max-width: 991.98px) {
    .features {
        margin-top: 4rem;
    }
}

.section-header h6 {
    font-size: 0.875rem;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.feature-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--light-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.feature-icon i {
    font-size: 1.75rem;
    color: var(--primary-color);
}

.feature-card:hover .feature-icon {
    background: var(--primary-color);
}

.feature-card:hover .feature-icon i {
    color: white;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.feature-card p {
    color: var(--gray-color);
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.feature-list li i {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

/* FAQ Section */
.faq-accordion .accordion-item {
    border: none;
    background: transparent;
    margin-bottom: 1rem;
}

.faq-accordion .accordion-button {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: var(--btn-border-radius) !important;
    padding: 1.25rem;
    font-weight: 500;
    color: var(--dark-color);
    box-shadow: none;
}

.faq-accordion .accordion-button:not(.collapsed) {
    color: var(--primary-color);
    background: white;
    box-shadow: var(--box-shadow);
}

.faq-accordion .accordion-button:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.faq-accordion .accordion-button::after {
    background-size: 1rem;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background-color: var(--light-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.faq-accordion .accordion-button:not(.collapsed)::after {
    background-color: var(--primary-color);
}

.faq-accordion .accordion-body {
    padding: 1.25rem;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-top: none;
    border-radius: 0 0 var(--btn-border-radius) var(--btn-border-radius);
    color: var(--gray-color);
}

/* Footer */
.footer {
    background: white;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.footer-brand {
    text-decoration: none;
    color: var(--dark-color);
}

.footer-brand:hover {
    color: var(--primary-color);
}

.footer h6 {
    font-weight: 600;
}

.footer ul {
    margin: 0;
    padding: 0;
}

.footer ul li {
    margin-bottom: 0.5rem;
}

.footer ul li a {
    color: var(--gray-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer ul li a:hover {
    color: var(--primary-color);
}

.social-links a {
    width: 36px;
    height: 36px;
    background: var(--light-color);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.footer-subscribe .form-control {
    border-radius: var(--btn-border-radius) 0 0 var(--btn-border-radius);
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0.75rem 1rem;
}

.footer-subscribe .btn {
    border-radius: 0 var(--btn-border-radius) var(--btn-border-radius) 0;
    padding: 0.75rem 1.5rem;
}

.footer hr {
    background: rgba(0, 0, 0, 0.05);
}

.footer .list-inline-item:not(:last-child) {
    margin-right: 1.5rem;
}

.footer .list-inline-item a {
    color: var(--gray-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer .list-inline-item a:hover {
    color: var(--primary-color);
}