/**
 * Enhanced Unified Dashboard JavaScript
 * Provides modern, responsive functionality across all dashboard sections
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize all dashboard features
  initializeDashboard();
});

/**
 * Main initialization function for the dashboard
 */
function initializeDashboard() {
  // Create animated background with particles
  createAnimatedBackground();

  // Add modern animation classes to elements
  animateElements();

  // Initialize enhanced toast notifications
  initializeToasts();

  // Add modern hover effects to cards and buttons
  addHoverEffects();

  // Setup responsive sidebar toggle
  setupSidebarToggle();

  // Update pending replies count
  updatePendingRepliesCount();

  // Initialize agent loading overlay
  initializeAgentLoadingOverlay();

  // Initialize modern form enhancements
  initializeFormEnhancements();

  // Initialize responsive table features
  initializeResponsiveTables();

  // Initialize scroll animations
  initializeScrollAnimations();

  // Initialize touch gestures for mobile
  initializeTouchGestures();

  // Initialize accessibility features
  initializeAccessibility();
}

/**
 * Creates an animated background with floating particles
 */
function createAnimatedBackground() {
  const dashboardBody = document.querySelector(".dashboard-body");
  if (!dashboardBody) return;

  // Create animated background container if it doesn't exist
  if (!document.querySelector(".animated-background")) {
    const animatedBg = document.createElement("div");
    animatedBg.className = "animated-background";
    dashboardBody.prepend(animatedBg);
  }

  // Create particles container if it doesn't exist
  if (!document.querySelector(".animated-particles")) {
    const particlesContainer = document.createElement("div");
    particlesContainer.className = "animated-particles";

    // Add particles
    const particleCount = 20;
    for (let i = 0; i < particleCount; i++) {
      createParticle(particlesContainer);
    }

    dashboardBody.prepend(particlesContainer);
  }
}

/**
 * Creates a single floating particle
 */
function createParticle(container) {
  const particle = document.createElement("div");
  particle.className = "particle";

  // Random size between 5px and 20px
  const size = Math.random() * 15 + 5;

  // Random position
  const posX = Math.random() * 100;
  const posY = Math.random() * 100;

  // Random opacity between 0.05 and 0.2
  const opacity = Math.random() * 0.15 + 0.05;

  // Random animation duration between 20s and 40s
  const duration = Math.random() * 20 + 20;

  // Random delay
  const delay = Math.random() * 10;

  // Style the particle
  particle.style.width = `${size}px`;
  particle.style.height = `${size}px`;
  particle.style.left = `${posX}%`;
  particle.style.top = `${posY}%`;
  particle.style.opacity = opacity.toString();
  particle.style.animation = `floatParticle ${duration}s linear infinite`;
  particle.style.animationDelay = `${delay}s`;

  // Add keyframes for floating animation if not already added
  if (!document.querySelector("#particle-keyframes")) {
    const style = document.createElement("style");
    style.id = "particle-keyframes";
    style.textContent = `
      @keyframes floatParticle {
        0% {
          transform: translate(0, 0);
        }
        25% {
          transform: translate(${Math.random() * 200 - 100}px, ${
      Math.random() * 200 - 100
    }px);
        }
        50% {
          transform: translate(${Math.random() * 200 - 100}px, ${
      Math.random() * 200 - 100
    }px);
        }
        75% {
          transform: translate(${Math.random() * 200 - 100}px, ${
      Math.random() * 200 - 100
    }px);
        }
        100% {
          transform: translate(0, 0);
        }
      }
    `;
    document.head.appendChild(style);
  }

  container.appendChild(particle);
}

/**
 * Adds animation classes to dashboard elements
 */
function animateElements() {
  const elementsToAnimate = [
    { selector: ".dashboard-card", className: "fade-in" },
    { selector: ".profile-card", className: "fade-in" },
    { selector: ".settings-card", className: "fade-in" },
    { selector: ".email-card", className: "slide-up" },
    { selector: ".stat-card", className: "slide-up" },
    { selector: ".timeline-item", className: "fade-in" },
    { selector: ".log-entry", className: "fade-in" },
    { selector: ".connected-service", className: "slide-up" },
  ];

  elementsToAnimate.forEach((item, index) => {
    const elements = document.querySelectorAll(item.selector);
    elements.forEach((element, elementIndex) => {
      element.classList.add(item.className);
      element.classList.add(`delay-${(elementIndex % 5) + 1}`);
    });
  });
}

/**
 * Initializes toast notifications
 */
function initializeToasts() {
  // Create a function to show toast notifications
  window.showToast = function (title, message, type = "success") {
    const toastContainer = document.getElementById("toastContainer");
    if (!toastContainer) return;

    // Create toast element
    const toast = document.createElement("div");
    toast.className = "toast";
    toast.setAttribute("role", "alert");
    toast.setAttribute("aria-live", "assertive");
    toast.setAttribute("aria-atomic", "true");

    // Set toast content
    toast.innerHTML = `
      <div class="toast-header bg-${type}">
        <strong class="me-auto text-white">${title}</strong>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
      <div class="toast-body">
        ${message}
      </div>
    `;

    // Add to container
    toastContainer.appendChild(toast);

    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast, {
      autohide: true,
      delay: 5000,
    });
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener("hidden.bs.toast", function () {
      toast.remove();
    });
  };
}

/**
 * Adds hover effects to cards and buttons
 */
function addHoverEffects() {
  // Add hover effect to cards
  const cards = document.querySelectorAll(
    ".dashboard-card, .profile-card, .settings-card, .email-card"
  );
  cards.forEach((card) => {
    card.addEventListener("mouseenter", () => {
      card.style.transform = "translateY(-5px)";
      card.style.boxShadow = "0 8px 25px rgba(0, 0, 0, 0.1)";
    });

    card.addEventListener("mouseleave", () => {
      card.style.transform = "translateY(0)";
      card.style.boxShadow = "0 4px 15px rgba(0, 0, 0, 0.05)";
    });
  });

  // Add hover effect to buttons
  const buttons = document.querySelectorAll(".action-btn");
  buttons.forEach((button) => {
    button.addEventListener("mouseenter", () => {
      button.style.transform = "translateY(-3px)";
    });

    button.addEventListener("mouseleave", () => {
      button.style.transform = "translateY(0)";
    });
  });

  // Add hover effect to sidebar links
  const navLinks = document.querySelectorAll(".sidebar .nav-link");
  navLinks.forEach((link) => {
    link.addEventListener("mouseenter", () => {
      const icon = link.querySelector("i");
      if (icon) {
        icon.style.transform = "translateX(3px)";
      }
    });

    link.addEventListener("mouseleave", () => {
      const icon = link.querySelector("i");
      if (icon) {
        icon.style.transform = "translateX(0)";
      }
    });
  });
}

/**
 * Sets up sidebar toggle functionality with proper desktop/mobile behavior
 */
function setupSidebarToggle() {
  const sidebarToggle = document.getElementById("sidebarToggle");
  const sidebarOverlay = document.getElementById("sidebarOverlay");

  if (sidebarToggle) {
    sidebarToggle.addEventListener("click", function () {
      document.body.classList.toggle("sb-sidenav-toggled");
    });
  }

  if (sidebarOverlay) {
    sidebarOverlay.addEventListener("click", function () {
      // Only close sidebar on mobile when overlay is clicked
      if (window.innerWidth < 992) {
        document.body.classList.remove("sb-sidenav-toggled");
      }
    });
  }

  // Handle window resize - ensure proper behavior on different screen sizes
  window.addEventListener("resize", function () {
    const isMobile = window.innerWidth < 992;
    const isToggled = document.body.classList.contains("sb-sidenav-toggled");

    if (isMobile) {
      // On mobile: if sidebar was open, close it when resizing
      if (isToggled) {
        document.body.classList.remove("sb-sidenav-toggled");
      }
    } else {
      // On desktop: ensure overlay is never visible
      // The CSS handles the sidebar visibility, we just need to ensure
      // the toggle state is consistent
    }
  });

  // Initialize proper state on page load
  initializeSidebarState();
}

/**
 * Initialize sidebar state based on screen size
 */
function initializeSidebarState() {
  const isMobile = window.innerWidth < 992;

  if (isMobile) {
    // On mobile: ensure sidebar starts closed
    document.body.classList.remove("sb-sidenav-toggled");
  }
  // On desktop: let CSS handle the default state
}

/**
 * Updates the pending email replies count
 */
function updatePendingRepliesCount() {
  fetch("/api/email_replies")
    .then((response) => response.json())
    .then((data) => {
      const pendingRepliesCount = document.getElementById(
        "pending-replies-count"
      );
      if (pendingRepliesCount) {
        pendingRepliesCount.textContent = data.replies
          ? data.replies.length
          : "0";
      }
    })
    .catch((error) => {
      console.error("Error fetching pending replies count:", error);
    });
}

/**
 * Initializes the agent loading overlay functionality
 */
function initializeAgentLoadingOverlay() {
  // Get the loading overlay element
  const agentLoadingOverlay = document.getElementById("agentLoadingOverlay");
  if (!agentLoadingOverlay) return;

  // Check if we have the start button
  const startBtn = document.getElementById("startBtn");
  const stopBtn = document.getElementById("stopBtn");

  // Set a maximum timeout to hide the overlay in case the status update doesn't catch it
  let loadingOverlayTimeout;

  // The event listeners for buttons are now managed in dashboard.html
  // This initialization function now just ensures the overlay is in the correct state on page load

  // Check if the overlay is already visible on page load (might happen in production)
  if (agentLoadingOverlay.classList.contains("show")) {
    console.log("Loading overlay was visible on page load, hiding it");
    // Hide it after a short delay to allow the page to fully load
    setTimeout(function () {
      hideAgentLoadingOverlay();
    }, 1000);
  }
}

/**
 * Shows the agent loading overlay with custom text
 * @param {string} operation - The operation being performed ("start" or "stop")
 */
function showAgentLoadingOverlay(operation = "start") {
  const agentLoadingOverlay = document.getElementById("agentLoadingOverlay");
  if (!agentLoadingOverlay) return;

  // Update the loading text based on operation
  const loadingText = agentLoadingOverlay.querySelector(".agent-loading-text");
  if (loadingText) {
    loadingText.textContent =
      operation === "start" ? "Starting Email Agent" : "Stopping Email Agent";
  }

  // Update the subtext based on operation
  const loadingSubtext = agentLoadingOverlay.querySelector(
    ".agent-loading-subtext"
  );
  if (loadingSubtext) {
    loadingSubtext.textContent =
      operation === "start"
        ? "Please wait while we initialize your email assistant..."
        : "Please wait while the email assistant is shutting down...";
  }

  // Show the overlay with transition effect
  agentLoadingOverlay.classList.add("show");

  // Set a timeout to hide the overlay after a maximum time (failsafe)
  if (window.agentLoadingOverlayTimeout) {
    clearTimeout(window.agentLoadingOverlayTimeout);
  }

  window.agentLoadingOverlayTimeout = setTimeout(function () {
    console.log("Failsafe: Hiding agent loading overlay after timeout");
    hideAgentLoadingOverlay();
  }, 30000); // 30 seconds maximum wait time
}

/**
 * Hides the agent loading overlay
 */
function hideAgentLoadingOverlay() {
  const agentLoadingOverlay = document.getElementById("agentLoadingOverlay");
  if (agentLoadingOverlay) {
    agentLoadingOverlay.classList.remove("show");
  }

  // Clear any existing timeout
  if (window.agentLoadingOverlayTimeout) {
    clearTimeout(window.agentLoadingOverlayTimeout);
    window.agentLoadingOverlayTimeout = null;
  }
}

/**
 * Initializes modern form enhancements
 */
function initializeFormEnhancements() {
  // Add floating label effects
  const formControls = document.querySelectorAll(".form-control");
  formControls.forEach((control) => {
    // Add focus and blur effects
    control.addEventListener("focus", function () {
      this.parentElement.classList.add("focused");
    });

    control.addEventListener("blur", function () {
      if (!this.value) {
        this.parentElement.classList.remove("focused");
      }
    });

    // Check if field has value on load
    if (control.value) {
      control.parentElement.classList.add("focused");
    }
  });

  // Add ripple effect to buttons
  const buttons = document.querySelectorAll(".action-btn, .btn");
  buttons.forEach((button) => {
    button.addEventListener("click", function (e) {
      const ripple = document.createElement("span");
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      ripple.style.width = ripple.style.height = size + "px";
      ripple.style.left = x + "px";
      ripple.style.top = y + "px";
      ripple.classList.add("ripple");

      this.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });
}

/**
 * Initializes responsive table features
 */
function initializeResponsiveTables() {
  const tables = document.querySelectorAll(".table-responsive table");
  tables.forEach((table) => {
    // Add data labels for mobile view
    const headers = table.querySelectorAll("thead th");
    const rows = table.querySelectorAll("tbody tr");

    rows.forEach((row) => {
      const cells = row.querySelectorAll("td");
      cells.forEach((cell, index) => {
        if (headers[index]) {
          cell.setAttribute("data-label", headers[index].textContent.trim());
        }
      });
    });

    // Add responsive class
    table.parentElement.classList.add("table-responsive-cards");
  });
}

/**
 * Initializes scroll animations
 */
function initializeScrollAnimations() {
  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("animate-fade-in");
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);

  // Observe elements for scroll animations
  const elementsToObserve = document.querySelectorAll(
    ".dashboard-card, .stat-card, .timeline-item, .email-card"
  );
  elementsToObserve.forEach((element) => {
    observer.observe(element);
  });
}

/**
 * Initializes touch gestures for mobile devices
 */
function initializeTouchGestures() {
  let startX, startY, currentX, currentY;

  // Swipe to close sidebar on mobile
  const sidebar = document.querySelector(".sidebar");
  if (sidebar) {
    sidebar.addEventListener("touchstart", function (e) {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    });

    sidebar.addEventListener("touchmove", function (e) {
      if (!startX || !startY) return;

      currentX = e.touches[0].clientX;
      currentY = e.touches[0].clientY;

      const diffX = startX - currentX;
      const diffY = startY - currentY;

      // If horizontal swipe is greater than vertical
      if (Math.abs(diffX) > Math.abs(diffY)) {
        // Swipe left to close sidebar
        if (diffX > 50) {
          document.body.classList.remove("sb-sidenav-toggled");
        }
      }
    });

    sidebar.addEventListener("touchend", function () {
      startX = null;
      startY = null;
    });
  }

  // Pull to refresh gesture (placeholder for future implementation)
  let pullStartY = 0;
  let pullCurrentY = 0;

  document.addEventListener("touchstart", function (e) {
    pullStartY = e.touches[0].clientY;
  });

  document.addEventListener("touchmove", function (e) {
    pullCurrentY = e.touches[0].clientY;

    // Only trigger if at top of page
    if (window.scrollY === 0 && pullCurrentY > pullStartY + 100) {
      // Add visual feedback for pull to refresh
      document.body.classList.add("pull-to-refresh");
    }
  });

  document.addEventListener("touchend", function () {
    document.body.classList.remove("pull-to-refresh");
    pullStartY = 0;
    pullCurrentY = 0;
  });
}

/**
 * Initializes accessibility features
 */
function initializeAccessibility() {
  // Add keyboard navigation for custom elements
  const interactiveElements = document.querySelectorAll(
    ".action-btn, .nav-link, .dashboard-card"
  );

  interactiveElements.forEach((element) => {
    // Add tabindex if not already present
    if (!element.hasAttribute("tabindex") && !element.href) {
      element.setAttribute("tabindex", "0");
    }

    // Add keyboard event listeners
    element.addEventListener("keydown", function (e) {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        this.click();
      }
    });
  });

  // Add focus indicators
  document.addEventListener("keydown", function (e) {
    if (e.key === "Tab") {
      document.body.classList.add("keyboard-navigation");
    }
  });

  document.addEventListener("mousedown", function () {
    document.body.classList.remove("keyboard-navigation");
  });

  // Add ARIA labels for screen readers
  const statusBadge = document.getElementById("status-badge");
  if (statusBadge) {
    statusBadge.setAttribute("aria-live", "polite");
    statusBadge.setAttribute("aria-label", "Agent status");
  }

  // Add skip link for keyboard users
  if (!document.querySelector(".skip-link")) {
    const skipLink = document.createElement("a");
    skipLink.href = "#main-content";
    skipLink.className = "skip-link";
    skipLink.textContent = "Skip to main content";
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: var(--primary);
      color: white;
      padding: 8px;
      text-decoration: none;
      border-radius: 4px;
      z-index: 1000;
      transition: top 0.3s;
    `;

    skipLink.addEventListener("focus", function () {
      this.style.top = "6px";
    });

    skipLink.addEventListener("blur", function () {
      this.style.top = "-40px";
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
  }
}
