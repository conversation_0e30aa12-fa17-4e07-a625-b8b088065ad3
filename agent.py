import threading
import time
import json
from datetime import datetime, timed<PERSON>ta
import base64
from email.mime.text import MIMEText

from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google.auth.transport.requests import Request
import httpx
from openai import OpenAI

from models import User
from config import active_config as config

class EmailAgent:
    def __init__(self, user_id, app=None):
        self.user_id = user_id
        self.running = False
        self.thread = None
        self.openai_client = None
        self.app = app

        # Try to import app if not provided
        if not self.app:
            try:
                from app import app as flask_app
                self.app = flask_app
            except ImportError:
                print(f"[{datetime.now()}] Warning: Could not import Flask app")

        # Setup OpenRouter client
        openrouter_setup_success = self._setup_openrouter()
        if not openrouter_setup_success:
            self._log("Warning: OpenRouter client setup failed")

    def _setup_openrouter(self):
        """Initialize OpenRouter client"""
        try:
            if not config.OPENROUTER_API_KEY:
                self._log("Error: OpenRouter API key is missing")
                return False

            http_client = httpx.Client()
            self.openai_client = OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=config.OPENROUTER_API_KEY,
                http_client=http_client
            )
            return True
        except Exception as e:
            self._log(f"Error setting up OpenRouter client: {e}")
            return False

    def _get_gmail_service(self):
        """Build and return Gmail API service"""
        try:
            # Get token info from database using app context
            if not self.app:
                self._log("No app instance available")
                return None

            with self.app.app_context():
                # Get token info from database
                from models import OAuthToken
                oauth_token = OAuthToken.query.filter_by(user_id=self.user_id).first()
                if not oauth_token:
                    self._log("No OAuth token found in database for user")
                    return None

                # Get credentials from token
                token_info = oauth_token.get_credentials()
                if not token_info:
                    self._log("No credentials found in database for user")
                    return None

                # Log token info for debugging (without sensitive data)
                token_keys = list(token_info.keys()) if isinstance(token_info, dict) else "Not a dictionary"
                self._log(f"Token info keys: {token_keys}")

                # Check if token_info has required fields
                required_fields = ['token', 'refresh_token', 'token_uri', 'client_id', 'client_secret']
                missing_fields = [field for field in required_fields if field not in token_info]
                if missing_fields:
                    self._log(f"Token info missing required fields: {missing_fields}")
                    return None

                try:
                    # Create credentials from token info
                    creds = Credentials.from_authorized_user_info(token_info, config.GOOGLE_SCOPES)

                    # Check if credentials are expired and need refreshing
                    if creds.expired and creds.refresh_token:
                        self._log("Credentials expired, attempting to refresh")
                        try:
                            creds.refresh(Request())
                            # Create a token info dictionary
                            token_info = {
                                'token': creds.token,
                                'refresh_token': creds.refresh_token,
                                'token_uri': creds.token_uri,
                                'client_id': creds.client_id,
                                'client_secret': creds.client_secret,
                                'scopes': creds.scopes,
                                'expiry': creds.expiry.isoformat()
                            }
                            # Update the token in the database
                            oauth_token.token_info = json.dumps(token_info)
                            from models import db
                            db.session.commit()
                            self._log("Credentials refreshed successfully")
                        except Exception as e:
                            self._log(f"Error refreshing credentials: {str(e)}")
                            return None

                    # Build and return the Gmail service
                    self._log("Building Gmail service")
                    service = build('gmail', 'v1', credentials=creds)
                    self._log("Gmail service built successfully")
                    return service
                except Exception as e:
                    self._log(f"Error creating credentials: {str(e)}")
                    return None
        except Exception as e:
            self._log(f"Error in _get_gmail_service: {str(e)}")
            return None

    def _log(self, message):
        """Add a log entry for this user"""
        try:
            # Print to console first
            print(f"[{datetime.now()}] {message}")

            # Then try to log to database if we have an app instance
            if self.app:
                try:
                    with self.app.app_context():
                        from models import UserLog, db
                        user_log = UserLog(self.user_id, message)
                        db.session.add(user_log)
                        db.session.commit()
                except Exception as e:
                    print(f"Error logging to database: {e}")
            else:
                # If we don't have an app instance, just log to console
                print(f"[{datetime.now()}] (Console only) {message}")
        except Exception as e:
            print(f"Error in _log: {e}")

    def _fetch_unread_emails(self):
        """Fetch unread emails from Gmail"""
        service = self._get_gmail_service()
        if not service:
            self._log("Failed to get Gmail service")
            return []

        try:
            yesterday = datetime.now() - timedelta(days=1)
            yesterday_str = yesterday.strftime('%Y/%m/%d')
            query = f"is:unread after:{yesterday_str}"

            self._log(f"Searching for emails with query: {query}")
            results = service.users().messages().list(userId='me', q=query).execute()
            messages = results.get('messages', [])
            email_count = len(messages)
            self._log(f"Found {email_count} unread emails")

            # Increment the emails processed count in the database
            if email_count > 0 and self.app:
                try:
                    with self.app.app_context():
                        from models import UserStats
                        UserStats.increment_emails_processed(self.user_id, email_count)
                except Exception as e:
                    print(f"Error incrementing emails processed count: {e}")

            emails = []
            for message in messages:
                msg = service.users().messages().get(
                    userId='me',
                    id=message['id'],
                    format='full'
                ).execute()

                headers = msg['payload']['headers']
                subject = next((h['value'] for h in headers if h['name'].lower() == 'subject'), '(No subject)')
                sender = next((h['value'] for h in headers if h['name'].lower() == 'from'), '(No sender)')

                body = ''
                if 'parts' in msg['payload']:
                    for part in msg['payload']['parts']:
                        if part['mimeType'] == 'text/plain':
                            body = base64.urlsafe_b64decode(part['body']['data']).decode('utf-8')
                            break
                elif 'body' in msg['payload'] and 'data' in msg['payload']['body']:
                    body = base64.urlsafe_b64decode(msg['payload']['body']['data']).decode('utf-8')

                emails.append({
                    'id': message['id'],
                    'sender': sender,
                    'subject': subject,
                    'body': body
                })

            return emails
        except HttpError as error:
            self._log(f"Error fetching emails: {error}")
            return []

    def _generate_reply(self, sender, subject, body, tone='professional', length='medium', custom_instructions=None):
        """Generate a reply using OpenRouter API"""
        if not self.app:
            self._log("No app instance available, cannot generate reply")
            return None

        try:
            # Get user info and settings from database
            user_name = "User"
            signature = f"\nBest regards,\n{user_name}"

            with self.app.app_context():
                from models import User, UserSettings

                # Get user settings and user info
                user_settings = UserSettings.query.filter_by(user_id=self.user_id).first()
                user = User.query.get(self.user_id)

                if not user:
                    self._log(f"User {self.user_id} not found in database")
                else:
                    user_name = user.name

                if not user_settings:
                    signature = f'\nBest regards,\n{user_name}'
                else:
                    signature = user_settings.custom_signature or f'\nBest regards,\n{user_name}'

            # Extract recipient's name from the sender email
            recipient_name = "there"
            if '<' in sender and '>' in sender:
                name_part = sender.split('<')[0].strip()
                if name_part:
                    # If there are multiple names, use the first name
                    recipient_name = name_part.split()[0]
            elif '@' in sender:
                name_part = sender.split('@')[0].strip()
                if name_part:
                    # Capitalize the name if it's just an email username
                    recipient_name = name_part.capitalize()

            # Build the prompt with the specified parameters
            prompt = f"""You are an AI assistant replying to emails on behalf of {user_name}.
Email:
From: {sender}
Subject: {subject}
Body: {body}
---
Draft a reply in a {tone} tone with a {length} length.
"""

            # Add custom instructions if provided
            if custom_instructions:
                prompt += f"Incorporate the following instructions: {custom_instructions}\n"

            # Add signature and no-reply instruction
            prompt += f"Start with 'Dear {recipient_name},' and end with: {signature}\n\nIf no reply is needed (spam, etc.), return only: [NO REPLY NEEDED]"

            # Generate reply using OpenRouter API
            if not self.openai_client:
                self._log("OpenRouter client not available")
                return None

            self._log(f"Generating {tone}, {length} reply for {subject} from {sender}")
            completion = self.openai_client.chat.completions.create(
                model=config.OPENROUTER_MODEL,
                messages=[{"role": "user", "content": prompt}]
            )
            reply = completion.choices[0].message.content.strip()
            self._log(f"Reply generated: {reply[:50]}...")
            return reply
        except Exception as e:
            self._log(f"Error generating reply: {e}")
            return None

    def regenerate_reply(self, reply_id, tone=None, length=None, custom_instructions=None):
        """Regenerate a reply with different parameters"""
        if not self.app:
            self._log("No app instance available, cannot regenerate reply")
            return False

        try:
            with self.app.app_context():
                from models import EmailReply, db

                # Get the reply from the database
                reply = EmailReply.query.filter_by(id=reply_id, user_id=self.user_id).first()
                if not reply:
                    self._log(f"Reply with ID {reply_id} not found")
                    return False

                # Use provided parameters or fall back to existing ones
                tone = tone or reply.tone
                length = length or reply.length
                custom_instructions = custom_instructions or reply.custom_instructions

                # Generate a new reply
                new_reply = self._generate_reply(
                    sender=reply.sender,
                    subject=reply.subject,
                    body=reply.original_body,
                    tone=tone,
                    length=length,
                    custom_instructions=custom_instructions
                )

                if new_reply and new_reply != '[NO REPLY NEEDED]':
                    # Update the reply in the database
                    reply.reply_body = new_reply
                    reply.modified_reply = None  # Clear any previous modifications
                    reply.tone = tone
                    reply.length = length
                    reply.custom_instructions = custom_instructions
                    db.session.commit()
                    return True
                return False
        except Exception as e:
            self._log(f"Error regenerating reply: {e}")
            return False

    def _send_reply(self, email_id, to, subject, body):
        """Send a reply email using Gmail API"""
        service = self._get_gmail_service()
        if not service:
            self._log("Failed to get Gmail service for sending reply")
            return False

        try:
            message = MIMEText(body)
            message['to'] = to
            message['subject'] = config.REPLY_PREFIX + subject if not subject.startswith(config.REPLY_PREFIX) else subject

            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')
            service.users().messages().send(
                userId='me',
                body={'raw': raw_message}
            ).execute()

            self._log(f"Sent reply to {to}: {subject}")

            # Increment the replies sent count in the database
            if self.app:
                try:
                    with self.app.app_context():
                        from models import UserStats
                        UserStats.increment_replies_sent(self.user_id)
                except Exception as e:
                    print(f"Error incrementing replies sent count: {e}")

            return True
        except Exception as e:
            self._log(f"Error sending reply: {e}")
            return False

    def send_stored_reply(self, reply_id):
        """Send a stored reply from the database"""
        if not self.app:
            self._log("No app instance available, cannot send stored reply")
            return False

        try:
            with self.app.app_context():
                from models import EmailReply, db

                # Get the reply from the database
                reply = EmailReply.query.filter_by(id=reply_id, user_id=self.user_id).first()
                if not reply:
                    self._log(f"Reply with ID {reply_id} not found")
                    return False

                # Use the modified reply if available, otherwise use the original reply
                body = reply.modified_reply if reply.modified_reply else reply.reply_body

                # Send the reply
                if self._send_reply(reply.email_id, reply.recipient, reply.subject, body):
                    # Mark the email as read
                    self._mark_as_read(reply.email_id)

                    # Mark the reply as sent in the database
                    reply.status = 'sent'
                    db.session.commit()

                    return True
            return False
        except Exception as e:
            self._log(f"Error sending stored reply: {e}")
            return False

    def _mark_as_read(self, email_id):
        """Mark an email as read"""
        service = self._get_gmail_service()
        if not service:
            return False

        try:
            service.users().messages().modify(
                userId='me',
                id=email_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            return True
        except Exception as e:
            self._log(f"Error marking email as read: {e}")
            return False

    def _process_emails(self):
        """Process unread emails"""
        if not self.app:
            self._log("No app instance available, cannot process emails")
            self.running = False
            return

        while self.running:
            try:
                # Use app context for all database operations
                with self.app.app_context():
                    from models import EmailReply, UserSettings, db

                    # Get user settings
                    user_settings = UserSettings.query.filter_by(user_id=self.user_id).first()
                    if not user_settings:
                        self._log("Warning: User settings not found, using defaults")
                        settings = {
                            'agent_enabled': True,
                            'check_interval': config.DEFAULT_CHECK_INTERVAL,
                            'custom_signature': "Best regards,\nAI Assistant"
                        }
                    else:
                        settings = {
                            'agent_enabled': user_settings.agent_enabled,
                            'check_interval': user_settings.check_interval,
                            'custom_signature': user_settings.custom_signature
                        }

                    if not settings.get('agent_enabled', False):
                        self._log("Agent disabled in settings, stopping")
                        self.running = False
                        break

                    # Fetch unread emails
                    emails = self._fetch_unread_emails()
                    for email in emails:
                        # Check if we already have a reply for this email
                        existing_reply = EmailReply.get_reply_by_email_id(self.user_id, email['id'])
                        if existing_reply:
                            self._log(f"Reply already exists for email from {email['sender']} with subject: {email['subject']}")
                            continue

                        # Extract recipient from sender field (for reply)
                        recipient = email['sender']

                        # Generate reply
                        reply = self._generate_reply(email['sender'], email['subject'], email['body'])

                        if reply:
                            if reply == '[NO REPLY NEEDED]':
                                # Mark as read and continue if no reply needed
                                self._log(f"No reply needed for email from {email['sender']}")
                                self._mark_as_read(email['id'])
                            else:
                                # Store the reply in the database for user review
                                self._log(f"Storing reply for user review: {email['subject']}")
                                # Create reply in database
                                new_reply = EmailReply(
                                    user_id=self.user_id,
                                    email_id=email['id'],
                                    sender=email['sender'],
                                    recipient=recipient,
                                    subject=email['subject'],
                                    original_body=email['body'],
                                    reply_body=reply
                                )
                                db.session.add(new_reply)
                                db.session.commit()

                                # Mark the email as read to prevent duplicate processing
                                # We'll still see it in the pending replies list
                                self._mark_as_read(email['id'])
                                self._log(f"Marked email as read to prevent duplicate processing: {email['subject']}")
                        else:
                            # If reply generation failed, mark as read and continue
                            self._log(f"Failed to generate reply for email from {email['sender']}")
                            self._mark_as_read(email['id'])

                # Sleep outside of app context
                check_interval = settings.get('check_interval', config.DEFAULT_CHECK_INTERVAL)
                time.sleep(min(check_interval, config.MAX_CHECK_INTERVAL))
            except Exception as e:
                self._log(f"Error in email processing loop: {e}")
                time.sleep(config.DEFAULT_CHECK_INTERVAL)

    def start(self):
        """Start the email processing thread"""
        try:
            self._log(f"Attempting to start agent for user {self.user_id}")

            if self.running:
                self._log("Agent is already running")
                return True

            # Check if agent is enabled in settings
            self._log("Checking user settings")
            settings = User.get_settings(self.user_id)
            if not settings:
                self._log("Failed to start agent: User settings not found")
                # Try to create default settings
                self._log("Attempting to create default settings")
                settings = {
                    'agent_enabled': True,
                    'check_interval': 10,
                    'custom_signature': f"Best regards,\nAI Assistant"
                }
                User.update_settings(self.user_id, settings)
                settings = User.get_settings(self.user_id)
                if not settings:
                    self._log("Failed to create default settings")
                    return False
                self._log("Created default settings")

            self._log(f"User settings: agent_enabled={settings.get('agent_enabled', False)}")
            if not settings.get('agent_enabled', False):
                self._log("Failed to start agent: Agent is disabled in user settings")
                # Enable the agent
                self._log("Enabling agent in settings")
                User.update_settings(self.user_id, {'agent_enabled': True})
                settings = User.get_settings(self.user_id)
                if not settings or not settings.get('agent_enabled', False):
                    self._log("Failed to enable agent in settings")
                    return False
                self._log("Agent enabled in settings")

            # Check if we can get Gmail service
            self._log("Checking Gmail service")
            service = self._get_gmail_service()
            if not service:
                self._log("Failed to start agent: Could not get Gmail service")
                return False
            self._log("Gmail service is available")

            # Check if OpenRouter client is set up
            self._log("Checking OpenRouter client")
            if not self.openai_client:
                self._log("Failed to start agent: OpenRouter client is not set up")
                # Try to set up OpenRouter client
                self._log("Attempting to set up OpenRouter client")
                if not self._setup_openrouter():
                    self._log("Failed to set up OpenRouter client")
                    return False
                self._log("OpenRouter client set up successfully")

            # All checks passed, start the agent
            self._log("All checks passed, starting agent thread")
            self.running = True
            self.thread = threading.Thread(target=self._process_emails)
            self.thread.daemon = True
            self.thread.start()
            self._log("Email agent started")
            return True
        except Exception as e:
            self._log(f"Error starting agent: {str(e)}")
            return False

    def stop(self):
        """Stop the email processing thread"""
        if self.running:
            # Set the running flag to False to signal the thread to exit
            self._log("Stopping email agent...")
            self.running = False

            # Wait for the thread to finish with a timeout
            if self.thread and self.thread.is_alive():
                self._log("Waiting for agent thread to terminate...")
                try:
                    # Use a longer timeout in production mode
                    from config import is_production
                    timeout = 3.0 if is_production() else 1.0
                    self.thread.join(timeout=timeout)

                    # Check if thread is still alive after timeout
                    if self.thread.is_alive():
                        self._log("Warning: Agent thread did not terminate within timeout")
                    else:
                        self._log("Agent thread terminated successfully")
                except Exception as e:
                    self._log(f"Error waiting for thread to terminate: {e}")

            # Reset the thread reference
            self.thread = None
            self._log("Email agent stopped")
            return True

        self._log("Agent is already stopped")
        return False

# Global dictionary to store agent instances
agents = {}
