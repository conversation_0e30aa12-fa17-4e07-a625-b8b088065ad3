# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
myenv/
venv/
.env
.env.*
!.env.example

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
*.log
logs/
npm-debug.log*

# Runtime data
pids
*.pid
*.seed

# Distribution / packaging
dist/
build/
*.egg-info/

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
coverage.xml
*.cover

# Database
*.db
*.sqlite3

# Local development
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.swp
*.swo
*~

# Security
*.pem
*.key
*.crt
secrets.yaml
credentials.json