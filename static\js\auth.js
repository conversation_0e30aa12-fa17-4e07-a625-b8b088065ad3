// Auth Pages Enhanced JavaScript
document.addEventListener('DOMContentLoaded', function() {
  // Create animated background elements
  const authPage = document.querySelector('.auth-page');
  if (authPage && !document.querySelector('.animated-background')) {
    // Create animated background
    const animatedBg = document.createElement('div');
    animatedBg.className = 'animated-background';
    
    // Create gradient overlay
    const gradientOverlay = document.createElement('div');
    gradientOverlay.className = 'gradient-overlay';
    
    // Append elements
    animatedBg.appendChild(gradientOverlay);
    authPage.prepend(animatedBg);
  }
  
  // Add animation classes to elements
  const elementsToAnimate = [
    { selector: '.auth-logo', className: 'fade-in delay-1' },
    { selector: '.auth-title', className: 'slide-up delay-2' },
    { selector: '.auth-subtitle', className: 'slide-up delay-3' },
    { selector: '.features-list', className: 'fade-in delay-4' },
    { selector: '.btn-google', className: 'slide-up delay-5' },
    { selector: '.account-toggle', className: 'fade-in delay-5' }
  ];
  
  elementsToAnimate.forEach(item => {
    const element = document.querySelector(item.selector);
    if (element) {
      element.classList.add(item.className);
    }
  });
  
  // Feature items hover effect
  const featureItems = document.querySelectorAll('.feature-item');
  featureItems.forEach((item, index) => {
    // Add staggered animation delay
    item.style.animationDelay = `${0.4 + (index * 0.1)}s`;
    item.classList.add('fade-in');
    
    // Add hover effect for icon
    const icon = item.querySelector('i');
    if (icon) {
      item.addEventListener('mouseenter', () => {
        icon.style.transform = 'scale(1.2) rotate(10deg)';
      });
      
      item.addEventListener('mouseleave', () => {
        icon.style.transform = 'scale(1)';
      });
    }
  });
  
  // Button hover effect
  const googleBtn = document.querySelector('.btn-google');
  if (googleBtn) {
    googleBtn.addEventListener('mouseenter', () => {
      const googleIcon = googleBtn.querySelector('.google-icon');
      if (googleIcon) {
        googleIcon.style.transform = 'rotate(10deg)';
      }
    });
    
    googleBtn.addEventListener('mouseleave', () => {
      const googleIcon = googleBtn.querySelector('.google-icon');
      if (googleIcon) {
        googleIcon.style.transform = 'rotate(0)';
      }
    });
  }
  
  // Add subtle particle effect to background (optional)
  createParticles();
});

// Create subtle particle effect
function createParticles() {
  const authPage = document.querySelector('.auth-page');
  if (!authPage) return;
  
  const particlesContainer = document.createElement('div');
  particlesContainer.className = 'particles-container';
  particlesContainer.style.position = 'absolute';
  particlesContainer.style.top = '0';
  particlesContainer.style.left = '0';
  particlesContainer.style.width = '100%';
  particlesContainer.style.height = '100%';
  particlesContainer.style.overflow = 'hidden';
  particlesContainer.style.zIndex = '1';
  particlesContainer.style.pointerEvents = 'none';
  
  // Create particles
  const particleCount = 20;
  for (let i = 0; i < particleCount; i++) {
    createParticle(particlesContainer);
  }
  
  authPage.appendChild(particlesContainer);
}

function createParticle(container) {
  const particle = document.createElement('div');
  
  // Random size between 3px and 8px
  const size = Math.random() * 5 + 3;
  
  // Random position
  const posX = Math.random() * 100;
  const posY = Math.random() * 100;
  
  // Random opacity between 0.1 and 0.3
  const opacity = Math.random() * 0.2 + 0.1;
  
  // Random animation duration between 15s and 30s
  const duration = Math.random() * 15 + 15;
  
  // Random delay
  const delay = Math.random() * 5;
  
  // Style the particle
  particle.style.position = 'absolute';
  particle.style.width = `${size}px`;
  particle.style.height = `${size}px`;
  particle.style.borderRadius = '50%';
  particle.style.backgroundColor = '#ffffff';
  particle.style.opacity = opacity.toString();
  particle.style.left = `${posX}%`;
  particle.style.top = `${posY}%`;
  particle.style.animation = `floatParticle ${duration}s linear infinite`;
  particle.style.animationDelay = `${delay}s`;
  
  // Add keyframes for floating animation
  if (!document.querySelector('#particle-keyframes')) {
    const style = document.createElement('style');
    style.id = 'particle-keyframes';
    style.textContent = `
      @keyframes floatParticle {
        0% {
          transform: translate(0, 0);
        }
        25% {
          transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px);
        }
        50% {
          transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px);
        }
        75% {
          transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px);
        }
        100% {
          transform: translate(0, 0);
        }
      }
    `;
    document.head.appendChild(style);
  }
  
  container.appendChild(particle);
}
