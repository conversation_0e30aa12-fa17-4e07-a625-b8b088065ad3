<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    {% set seo_data = seo or get_seo_data('profile') %}
    <title>{{ seo_data.title }}</title>
    <meta name="description" content="{{ seo_data.description }}" />
    <meta name="keywords" content="{{ seo_data.keywords }}" />
    <meta name="author" content="Email Reply Agent" />
    <meta name="robots" content="noindex, nofollow" />
    <link rel="canonical" href="{{ get_canonical_url('profile') }}" />

    <!-- Enhanced Favicon Implementation -->
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="{{ url_for('static', filename='img/favicon-16x16.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="{{ url_for('static', filename='img/favicon-32x32.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="48x48"
      href="{{ url_for('static', filename='img/favicon-48x48.png') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="{{ url_for('static', filename='img/apple-touch-icon.png') }}"
    />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='manifest.json') }}"
    />
    <meta name="theme-color" content="#1E90FF" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/styles.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard-enhanced.css') }}"
    />
  </head>
  <body class="dashboard-body">
    <!-- Toast Container for Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Topbar -->
    <nav id="topbar">
      <button class="btn" id="sidebarToggle" type="button">
        <i class="bi bi-list"></i>
      </button>
      <a class="navbar-brand" href="{{ url_for('dashboard') }}">
        <i class="bi bi-envelope-paper-heart"></i>
        Email Reply Agent
      </a>
      <div class="topbar-actions">
        <div class="user-info">
          <div class="user-avatar">{{ current_user.name[0] }}</div>
          <div class="user-name">{{ current_user.name }}</div>
        </div>
        <a href="{{ url_for('logout') }}" class="btn-logout">
          <i class="bi bi-box-arrow-right"></i>Logout
        </a>
      </div>
    </nav>

    <!-- Sidebar Overlay (for mobile) -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <div class="sidebar">
      <div class="sidebar-content">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a href="{{ url_for('dashboard') }}" class="nav-link">
              <i class="bi bi-speedometer2"></i>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('email_preview') }}" class="nav-link">
              <i class="bi bi-envelope"></i>
              Email Replies
              <span class="badge" id="pending-replies-count">0</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('profile') }}" class="nav-link active">
              <i class="bi bi-person"></i>
              Profile
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('settings') }}" class="nav-link">
              <i class="bi bi-gear"></i>
              Settings
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Content -->
    <main class="content">
      <div class="container-fluid">
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %} {% for category, message in messages %}
        <div
          class="alert alert-{{ category }} alert-dismissible fade show"
          role="alert"
        >
          {{ message }}
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="alert"
          ></button>
        </div>
        {% endfor %} {% endif %} {% endwith %}

        <div class="row">
          <div class="col-lg-4">
            <div class="profile-card mb-4">
              <div class="card-body text-center">
                <img
                  src="{{ current_user.picture }}"
                  alt="Profile Picture"
                  class="profile-image"
                />
                <h4 class="profile-name">{{ current_user.name }}</h4>
                <p class="profile-email">{{ current_user.email }}</p>
                <div class="d-flex justify-content-center">
                  <a
                    href="{{ url_for('settings') }}"
                    class="action-btn btn-primary"
                  >
                    <i class="bi bi-gear"></i>
                    Edit Settings
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-8">
            <div class="profile-card mb-4">
              <div class="card-header">
                <h5>Account Information</h5>
              </div>
              <div class="card-body">
                <div class="profile-info-row">
                  <div class="profile-info-label">Account Type</div>
                  <div class="profile-info-value">Free Plan</div>
                </div>
                <div class="profile-info-row">
                  <div class="profile-info-label">Member Since</div>
                  <div class="profile-info-value">
                    {{ current_user.created_at.strftime('%B %d, %Y') }}
                  </div>
                </div>
                <div class="profile-info-row">
                  <div class="profile-info-label">Replies Available</div>
                  <div class="profile-info-value">10/day (Free Plan)</div>
                </div>
              </div>
            </div>

            <div class="profile-card">
              <div class="card-header">
                <h5>Connected Services</h5>
              </div>
              <div class="card-body">
                <div class="connected-service">
                  <div class="connected-service-icon">
                    <i class="bi bi-google"></i>
                  </div>
                  <div class="connected-service-info">
                    <h6 class="connected-service-name">Gmail</h6>
                    <p class="connected-service-email">
                      Connected via Google OAuth
                    </p>
                  </div>
                  <span class="connected-service-status">Active</span>
                </div>
                <div class="connected-service">
                  <div class="connected-service-icon">
                    <i class="bi bi-robot"></i>
                  </div>
                  <div class="connected-service-info">
                    <h6 class="connected-service-name">OpenRouter AI</h6>
                    <p class="connected-service-email">API Integration</p>
                  </div>
                  <span class="connected-service-status">Active</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/unified-dashboard.js') }}"></script>
  </body>
</html>
