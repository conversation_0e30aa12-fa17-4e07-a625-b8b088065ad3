"""Change UserSettings and UserStats models to use user_id as primary key

Revision ID: 728bb708c342
Revises: 9bf8379bcaff
Create Date: 2025-04-27 09:37:03.762503

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '728bb708c342'
down_revision = '9bf8379bcaff'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_settings', schema=None) as batch_op:
        batch_op.drop_column('id')

    with op.batch_alter_table('user_stats', schema=None) as batch_op:
        batch_op.drop_column('id')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_stats', schema=None) as batch_op:
        batch_op.add_column(sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False))

    with op.batch_alter_table('user_settings', schema=None) as batch_op:
        batch_op.add_column(sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False))

    # ### end Alembic commands ###
