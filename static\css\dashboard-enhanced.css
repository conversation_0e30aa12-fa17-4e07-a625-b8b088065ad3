/* Enhanced Dashboard Responsive Design - Modern Mobile-First Approach */

/* Enhanced Table Responsiveness */
.table-responsive-cards {
  border: none;
}

@media (max-width: 767px) {
  .table-responsive-cards thead {
    display: none;
  }

  .table-responsive-cards tbody tr {
    display: block;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 1rem;
    box-shadow: var(--box-shadow-card);
    background: var(--gradient-card);
    transition: all var(--transition-fast);
  }

  .table-responsive-cards tbody tr:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-card-hover);
  }

  .table-responsive-cards tbody td {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: none;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(7, 122, 125, 0.1);
  }

  .table-responsive-cards tbody td:last-child {
    border-bottom: none;
  }

  .table-responsive-cards tbody td:before {
    content: attr(data-label);
    font-weight: 600;
    margin-right: 1rem;
    color: var(--dark);
    font-family: var(--font-primary);
  }

  .table-responsive-cards tbody td:last-child {
    text-align: right;
    justify-content: flex-end;
  }
}

/* Enhanced Modal Responsiveness */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--box-shadow-hover);
  }

  .modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(7, 122, 125, 0.1);
  }

  .modal-body {
    padding: 1.5rem;
  }

  .modal-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(7, 122, 125, 0.1);
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .modal-footer .btn {
    width: 100%;
  }
}

/* Enhanced Form Responsiveness */
@media (max-width: 768px) {
  .form-group {
    margin-bottom: var(--spacing-md);
  }

  .form-label {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--spacing-xs);
    font-family: var(--font-primary);
  }

  .form-control {
    border-radius: var(--border-radius-md);
    border: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all var(--transition-fast);
    background: var(--white);
  }

  .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(7, 122, 125, 0.1);
    outline: none;
  }

  .input-group {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .input-group-text {
    border-radius: var(--border-radius-md);
    background: var(--gradient-card);
    border: 2px solid var(--border-color);
    font-weight: 500;
    color: var(--dark);
  }
}

/* Enhanced Navigation for Mobile */
@media (max-width: 992px) {
  .sidebar {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }

  .sidebar .nav-link {
    padding: 1rem 1.5rem;
    margin: 0.25rem 1rem;
    border-radius: var(--border-radius-md);
    border-left: none;
  }

  .sidebar .nav-link.active {
    background: var(--gradient-btn-primary);
    box-shadow: var(--box-shadow-btn);
  }

  .sidebar .nav-link:hover {
    background: rgba(7, 122, 125, 0.1);
    transform: translateX(5px);
  }
}

/* Enhanced Grid System */
@media (max-width: 768px) {
  .dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .dashboard-grid-2 {
    grid-template-columns: 1fr;
  }

  .dashboard-grid-3 {
    grid-template-columns: 1fr;
  }

  .dashboard-grid-4 {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 769px) {
  .dashboard-grid-2 {
    grid-template-columns: 1fr 1fr;
  }

  .dashboard-grid-3 {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .dashboard-grid-4 {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

/* Enhanced Stat Cards */
.stat-card-enhanced {
  background: var(--gradient-card);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  box-shadow: var(--box-shadow-card);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.stat-card-enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-btn-primary);
}

.stat-card-enhanced:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-card-hover);
}

.stat-card-enhanced .stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-btn-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
}

.stat-card-enhanced .stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: var(--spacing-xs);
  font-family: var(--font-primary);
}

.stat-card-enhanced .stat-label {
  color: var(--gray);
  font-weight: 500;
  font-size: 0.9rem;
}

@media (max-width: 576px) {
  .stat-card-enhanced {
    padding: var(--spacing-sm);
  }

  .stat-card-enhanced .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stat-card-enhanced .stat-value {
    font-size: 2rem;
  }
}

/* Enhanced Alert Styles */
.alert-enhanced {
  border: none;
  border-radius: var(--border-radius-md);
  padding: 1rem 1.5rem;
  margin-bottom: var(--spacing-md);
  box-shadow: var(--box-shadow-card);
  position: relative;
  overflow: hidden;
}

.alert-enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
}

.alert-enhanced.alert-success {
  background: linear-gradient(
    135deg,
    rgba(45, 170, 158, 0.1) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  color: var(--success);
}

.alert-enhanced.alert-success::before {
  background: var(--success);
}

.alert-enhanced.alert-danger {
  background: linear-gradient(
    135deg,
    rgba(220, 53, 69, 0.1) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  color: var(--danger);
}

.alert-enhanced.alert-danger::before {
  background: var(--danger);
}

.alert-enhanced.alert-warning {
  background: linear-gradient(
    135deg,
    rgba(255, 193, 7, 0.1) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  color: var(--warning);
}

.alert-enhanced.alert-warning::before {
  background: var(--warning);
}

.alert-enhanced.alert-info {
  background: linear-gradient(
    135deg,
    rgba(7, 122, 125, 0.1) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  color: var(--info);
}

.alert-enhanced.alert-info::before {
  background: var(--info);
}

/* Enhanced Progress Bars */
.progress-enhanced {
  height: 8px;
  border-radius: 50px;
  background: var(--gray-light);
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-enhanced .progress-bar {
  background: var(--gradient-btn-primary);
  border-radius: 50px;
  transition: width var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.progress-enhanced .progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s ease-in-out infinite;
}

/* Enhanced Modal Styles */
.enhanced-modal {
  border: none;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(245, 238, 221, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.enhanced-modal-header {
  background: linear-gradient(135deg, #077a7d 0%, #06595b 100%);
  color: white;
  padding: 2rem;
  border-bottom: none;
  position: relative;
}

.enhanced-modal-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 100%
  );
  pointer-events: none;
}

.modal-title-container {
  position: relative;
  z-index: 1;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0;
}

.modal-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  backdrop-filter: blur(5px);
}

.modal-title-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.title-main {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: var(--font-primary);
}

.title-sub {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 400;
}

.enhanced-btn-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.enhanced-btn-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.enhanced-modal-body {
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.95);
}

.enhanced-form {
  max-width: 100%;
}

.form-section {
  margin-bottom: 2rem;
}

.form-group-enhanced {
  margin-bottom: 2rem;
}

.form-label-enhanced {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-family: var(--font-primary);
}

.input-container-enhanced {
  position: relative;
  margin-bottom: 1rem;
}

.input-group-enhanced {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.input-group-enhanced:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(7, 122, 125, 0.1);
  transform: translateY(-2px);
}

.input-icon {
  padding: 1rem 1.25rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: var(--primary);
  font-size: 1.1rem;
  border-right: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
}

.form-control-enhanced {
  flex: 1;
  border: none;
  outline: none;
  padding: 1.25rem 1.5rem;
  font-size: 1.1rem;
  background: transparent;
  color: var(--dark);
  font-family: var(--font-body);
}

.form-control-enhanced::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.form-help-text {
  margin-top: 1rem;
  padding: 1.25rem;
  background: linear-gradient(
    135deg,
    rgba(7, 122, 125, 0.05) 0%,
    rgba(122, 226, 207, 0.05) 100%
  );
  border-radius: 12px;
  border-left: 4px solid var(--primary);
}

.help-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  color: var(--gray);
  line-height: 1.5;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-item i {
  color: var(--primary);
  margin-top: 0.1rem;
  flex-shrink: 0;
}

.feature-highlight {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(
    135deg,
    rgba(122, 226, 207, 0.1) 0%,
    rgba(7, 122, 125, 0.05) 100%
  );
  border-radius: 15px;
  border: 1px solid rgba(7, 122, 125, 0.1);
}

.feature-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--secondary-dark) 100%
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.feature-content h6 {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: var(--dark);
  font-family: var(--font-primary);
}

.feature-content p {
  margin: 0;
  color: var(--gray);
  line-height: 1.6;
  font-size: 0.95rem;
}

.enhanced-alert {
  border: none;
  border-radius: 12px;
  padding: 1rem 1.25rem;
  margin-top: 1.5rem;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.enhanced-alert.alert-danger {
  background: linear-gradient(
    135deg,
    rgba(220, 53, 69, 0.1) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  color: #dc3545;
  border-left: 4px solid #dc3545;
}

.enhanced-alert.alert-success {
  background: linear-gradient(
    135deg,
    rgba(45, 170, 158, 0.1) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  color: var(--success);
  border-left: 4px solid var(--success);
}

.enhanced-modal-footer {
  padding: 2rem 2.5rem;
  background: rgba(248, 249, 250, 0.8);
  border-top: 1px solid rgba(7, 122, 125, 0.1);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.enhanced-btn-secondary {
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  border: 2px solid #e0e0e0;
  background: white;
  color: var(--gray);
  transition: all 0.3s ease;
  min-height: 48px;
  display: flex;
  align-items: center;
}

.enhanced-btn-secondary:hover {
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enhanced-btn-primary {
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  );
  border: none;
  color: white;
  transition: all 0.3s ease;
  min-height: 48px;
  position: relative;
  overflow: hidden;
}

.enhanced-btn-primary:hover {
  background: linear-gradient(
    135deg,
    var(--primary-dark) 0%,
    var(--primary) 100%
  );
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(7, 122, 125, 0.3);
}

.enhanced-btn-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.btn-spinner {
  display: flex;
  align-items: center;
}

/* Mobile Responsive Enhancements for Modal */
@media (max-width: 768px) {
  .modal-lg {
    max-width: calc(100vw - 2rem);
  }

  .enhanced-modal-header {
    padding: 1.5rem;
  }

  .modal-title {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .modal-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .title-main {
    font-size: 1.25rem;
  }

  .enhanced-modal-body {
    padding: 1.5rem;
  }

  .enhanced-modal-footer {
    padding: 1.5rem;
    flex-direction: column;
  }

  .enhanced-btn-secondary,
  .enhanced-btn-primary {
    width: 100%;
    justify-content: center;
  }

  .feature-highlight {
    flex-direction: column;
    text-align: center;
  }

  .form-help-text {
    padding: 1rem;
  }
}
