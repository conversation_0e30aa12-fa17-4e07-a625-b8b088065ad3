<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    {% set seo_data = seo or get_seo_data('settings') %}
    <title>{{ seo_data.title }}</title>
    <meta name="description" content="{{ seo_data.description }}" />
    <meta name="keywords" content="{{ seo_data.keywords }}" />
    <meta name="author" content="Email Reply Agent" />
    <meta name="robots" content="noindex, nofollow" />
    <link rel="canonical" href="{{ get_canonical_url('settings') }}" />

    <!-- Enhanced Favicon Implementation -->
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="{{ url_for('static', filename='img/favicon-16x16.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="{{ url_for('static', filename='img/favicon-32x32.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="48x48"
      href="{{ url_for('static', filename='img/favicon-48x48.png') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="{{ url_for('static', filename='img/apple-touch-icon.png') }}"
    />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='manifest.json') }}"
    />
    <meta name="theme-color" content="#1E90FF" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/styles.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard-enhanced.css') }}"
    />
  </head>
  <body class="dashboard-body">
    <!-- Toast Container for Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Topbar -->
    <nav id="topbar">
      <button class="btn" id="sidebarToggle" type="button">
        <i class="bi bi-list"></i>
      </button>
      <a class="navbar-brand" href="{{ url_for('dashboard') }}">
        <i class="bi bi-envelope-paper-heart"></i>
        Email Reply Agent
      </a>
      <div class="topbar-actions">
        <div class="user-info">
          <div class="user-avatar">{{ current_user.name[0] }}</div>
          <div class="user-name">{{ current_user.name }}</div>
        </div>
        <a href="{{ url_for('logout') }}" class="btn-logout">
          <i class="bi bi-box-arrow-right"></i>Logout
        </a>
      </div>
    </nav>

    <!-- Sidebar Overlay (for mobile) -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <div class="sidebar">
      <div class="sidebar-content">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a href="{{ url_for('dashboard') }}" class="nav-link">
              <i class="bi bi-speedometer2"></i>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('email_preview') }}" class="nav-link">
              <i class="bi bi-envelope"></i>
              Email Replies
              <span class="badge" id="pending-replies-count">0</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('profile') }}" class="nav-link">
              <i class="bi bi-person"></i>
              Profile
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('settings') }}" class="nav-link active">
              <i class="bi bi-gear"></i>
              Settings
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Content -->
    <main class="content">
      <div class="container-fluid">
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %} {% for category, message in messages %}
        <div
          class="alert alert-{{ category }} alert-dismissible fade show"
          role="alert"
        >
          {{ message }}
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="alert"
          ></button>
        </div>
        {% endfor %} {% endif %} {% endwith %}

        <div class="row">
          <div class="col-lg-8">
            <div class="dashboard-card mb-4">
              <div class="card-header">
                <h5>Email Agent Settings</h5>
              </div>
              <div class="card-body">
                <form method="POST" action="{{ url_for('settings') }}">
                  <div class="mb-4">
                    <label class="form-label">Check Interval</label>
                    <div class="input-group">
                      <input
                        type="number"
                        name="check_interval"
                        class="form-control"
                        value="{{ settings.check_interval }}"
                        min="10"
                        max="60"
                      />
                      <span class="input-group-text">seconds</span>
                    </div>
                    <div class="form-text">
                      <i class="bi bi-info-circle"></i>
                      How often the agent checks for new emails (10-60 seconds)
                    </div>
                  </div>

                  <div class="mb-4">
                    <label class="form-label">Custom Email Signature</label>
                    <textarea
                      name="custom_signature"
                      class="form-control"
                      rows="3"
                      placeholder="Best regards,&#10;Your Name"
                    >
{{ settings.custom_signature }}</textarea
                    >
                    <div class="form-text">
                      <i class="bi bi-info-circle"></i>
                      Your signature will be added to all automated replies
                    </div>
                  </div>

                  <div class="mb-4">
                    <label class="form-label">Email Preferences</label>
                    <div class="settings-option-group">
                      <div class="form-check mb-2">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="skipPromotions"
                          checked
                          disabled
                        />
                        <label class="form-check-label" for="skipPromotions">
                          Skip promotional emails
                        </label>
                      </div>
                      <div class="form-check mb-2">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          id="skipNewsletters"
                          checked
                          disabled
                        />
                        <label class="form-check-label" for="skipNewsletters">
                          Skip newsletter emails
                        </label>
                      </div>
                      <div class="pro-feature-badge">
                        <i class="bi bi-star-fill"></i> Pro Feature
                      </div>
                    </div>
                  </div>

                  <button type="submit" class="action-btn btn-primary">
                    <i class="bi bi-save"></i>
                    Save Changes
                  </button>
                </form>
              </div>
            </div>

            <div class="dashboard-card">
              <div class="card-header">
                <h5>Notification Settings</h5>
              </div>
              <div class="card-body">
                <div class="mb-4">
                  <label class="form-label">Email Notifications</label>
                  <div class="settings-option-group">
                    <div class="form-check mb-2">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="notifyReplies"
                        checked
                        disabled
                      />
                      <label class="form-check-label" for="notifyReplies">
                        Notify me when replies are sent
                      </label>
                    </div>
                    <div class="form-check mb-2">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="notifyErrors"
                        checked
                        disabled
                      />
                      <label class="form-check-label" for="notifyErrors">
                        Notify me about errors
                      </label>
                    </div>
                    <div class="pro-feature-badge">
                      <i class="bi bi-star-fill"></i> Pro Feature
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-4">
            <div class="dashboard-card mb-4">
              <div class="card-header">
                <h5>Plan Details</h5>
              </div>
              <div class="card-body">
                <div class="plan-details">
                  <div class="plan-icon">
                    <i class="bi bi-lightning"></i>
                  </div>
                  <div class="plan-name">Free</div>
                  <div class="plan-limit">10 replies/day</div>
                  <div class="plan-features">
                    <div class="plan-feature">
                      <i class="bi bi-check-circle"></i>
                      <span>Basic email automation</span>
                    </div>
                    <div class="plan-feature">
                      <i class="bi bi-check-circle"></i>
                      <span>AI-generated replies</span>
                    </div>
                    <div class="plan-feature disabled">
                      <i class="bi bi-x-circle"></i>
                      <span>Custom email filters</span>
                    </div>
                    <div class="plan-feature disabled">
                      <i class="bi bi-x-circle"></i>
                      <span>Priority support</span>
                    </div>
                  </div>
                  <a href="#" class="action-btn btn-primary">
                    <i class="bi bi-star-fill"></i>
                    Upgrade to Pro
                  </a>
                </div>
              </div>
            </div>

            <div class="dashboard-card">
              <div class="card-header">
                <h5>Account Actions</h5>
              </div>
              <div class="card-body">
                <div class="account-actions">
                  <button
                    class="action-btn btn-outline-danger"
                    type="button"
                    data-bs-toggle="modal"
                    data-bs-target="#disconnectModal"
                  >
                    <i class="bi bi-x-circle"></i>
                    Disconnect Gmail
                  </button>
                  <button
                    class="action-btn btn-outline-danger"
                    type="button"
                    data-bs-toggle="modal"
                    data-bs-target="#deleteModal"
                  >
                    <i class="bi bi-trash"></i>
                    Delete Account
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Disconnect Modal -->
    <div class="modal fade" id="disconnectModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-x-circle text-danger me-2"></i>
              Disconnect Gmail Account
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle"></i>
              <span>
                Are you sure you want to disconnect your Gmail account? This
                will stop all email automation.
              </span>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="action-btn btn-outline-primary"
              data-bs-dismiss="modal"
            >
              <i class="bi bi-x-circle"></i> Cancel
            </button>
            <button type="button" class="action-btn btn-danger">
              <i class="bi bi-link-break"></i> Disconnect
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Account Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-trash text-danger me-2"></i>
              Delete Account
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <div class="alert alert-danger">
              <i class="bi bi-exclamation-triangle"></i>
              <span>
                Are you sure you want to delete your account? This action cannot
                be undone.
              </span>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="action-btn btn-outline-primary"
              data-bs-dismiss="modal"
            >
              <i class="bi bi-x-circle"></i> Cancel
            </button>
            <button type="button" class="action-btn btn-danger">
              <i class="bi bi-trash"></i> Delete Account
            </button>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/unified-dashboard.js') }}"></script>
    <script>
      // Add custom settings page functionality here if needed
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize form controls with custom styling
        initializeFormControls();
      });

      function initializeFormControls() {
        // Add focus effects to form inputs
        const formInputs = document.querySelectorAll(".form-control");
        formInputs.forEach((input) => {
          input.addEventListener("focus", function () {
            this.style.borderColor = "var(--accent)";
            this.style.boxShadow = "0 0 0 0.2rem rgba(40, 167, 69, 0.25)";
          });

          input.addEventListener("blur", function () {
            this.style.borderColor = "";
            this.style.boxShadow = "";
          });
        });

        // Show success message when form is submitted
        const settingsForm = document.querySelector("form");
        if (settingsForm) {
          settingsForm.addEventListener("submit", function (e) {
            // We don't prevent default here to allow the form to submit normally
            // Just show a toast notification
            if (window.showToast) {
              window.showToast(
                "Success",
                "Your settings have been saved successfully!",
                "success"
              );
            }
          });
        }
      }
    </script>
  </body>
</html>
