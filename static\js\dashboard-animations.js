/**
 * Dashboard Animations and Enhanced UI
 * Adds animations, particle effects, and UI enhancements to the dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
  // Create animated background with particles
  createAnimatedBackground();
  
  // Add animation classes to elements
  animateElements();
  
  // Initialize toast notifications
  initializeToasts();
  
  // Add hover effects to cards and buttons
  addHoverEffects();
  
  // Add pulse animation to running status badge
  const statusBadge = document.getElementById('status-badge');
  if (statusBadge && statusBadge.textContent === 'Running') {
    statusBadge.classList.add('pulse-animation');
  }
  
  // Add animation to new log entries
  observeLogChanges();
});

/**
 * Creates an animated background with floating particles
 */
function createAnimatedBackground() {
  const dashboardBody = document.querySelector('.dashboard-body');
  if (!dashboardBody) return;
  
  // Create animated background container
  const animatedBg = document.createElement('div');
  animatedBg.className = 'animated-background';
  
  // Create particles container
  const particlesContainer = document.createElement('div');
  particlesContainer.className = 'animated-particles';
  
  // Add particles
  const particleCount = 20;
  for (let i = 0; i < particleCount; i++) {
    createParticle(particlesContainer);
  }
  
  // Append to body
  dashboardBody.prepend(animatedBg);
  dashboardBody.prepend(particlesContainer);
}

/**
 * Creates a single floating particle
 */
function createParticle(container) {
  const particle = document.createElement('div');
  particle.className = 'particle';
  
  // Random size between 5px and 20px
  const size = Math.random() * 15 + 5;
  
  // Random position
  const posX = Math.random() * 100;
  const posY = Math.random() * 100;
  
  // Random opacity between 0.05 and 0.2
  const opacity = Math.random() * 0.15 + 0.05;
  
  // Random animation duration between 20s and 40s
  const duration = Math.random() * 20 + 20;
  
  // Random delay
  const delay = Math.random() * 10;
  
  // Style the particle
  particle.style.width = `${size}px`;
  particle.style.height = `${size}px`;
  particle.style.left = `${posX}%`;
  particle.style.top = `${posY}%`;
  particle.style.opacity = opacity.toString();
  particle.style.animation = `floatParticle ${duration}s linear infinite`;
  particle.style.animationDelay = `${delay}s`;
  
  // Add keyframes for floating animation if not already added
  if (!document.querySelector('#particle-keyframes')) {
    const style = document.createElement('style');
    style.id = 'particle-keyframes';
    style.textContent = `
      @keyframes floatParticle {
        0% {
          transform: translate(0, 0);
        }
        25% {
          transform: translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px);
        }
        50% {
          transform: translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px);
        }
        75% {
          transform: translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px);
        }
        100% {
          transform: translate(0, 0);
        }
      }
    `;
    document.head.appendChild(style);
  }
  
  container.appendChild(particle);
}

/**
 * Adds animation classes to dashboard elements
 */
function animateElements() {
  const elementsToAnimate = [
    { selector: '.dashboard-card', className: 'fade-in' },
    { selector: '.stat-card', className: 'slide-up' },
    { selector: '.timeline-item', className: 'fade-in' },
    { selector: '.log-entry', className: 'fade-in' },
    { selector: '.email-item', className: 'slide-up' }
  ];
  
  elementsToAnimate.forEach((item, index) => {
    const elements = document.querySelectorAll(item.selector);
    elements.forEach((element, elementIndex) => {
      element.classList.add(item.className);
      element.classList.add(`delay-${(elementIndex % 5) + 1}`);
    });
  });
}

/**
 * Initializes toast notifications
 */
function initializeToasts() {
  // Create a function to show toast notifications
  window.showToast = function(title, message, type = 'success') {
    const toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) return;
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    // Set toast content
    toast.innerHTML = `
      <div class="toast-header bg-${type}">
        <strong class="me-auto text-white">${title}</strong>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
      <div class="toast-body">
        ${message}
      </div>
    `;
    
    // Add to container
    toastContainer.appendChild(toast);
    
    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast, {
      autohide: true,
      delay: 5000
    });
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
      toast.remove();
    });
  };
  
  // Override the console.log for agent status messages to show toasts
  const originalStartAgent = window.startAgent;
  if (originalStartAgent) {
    window.startAgent = function() {
      originalStartAgent();
      showToast('Agent Status', 'Agent started successfully', 'success');
    };
  }
  
  const originalStopAgent = window.stopAgent;
  if (originalStopAgent) {
    window.stopAgent = function() {
      originalStopAgent();
      showToast('Agent Status', 'Agent stopped successfully', 'danger');
    };
  }
}

/**
 * Adds hover effects to cards and buttons
 */
function addHoverEffects() {
  // Add hover effect to stat cards
  const statCards = document.querySelectorAll('.stat-card');
  statCards.forEach(card => {
    card.addEventListener('mouseenter', () => {
      const icon = card.querySelector('i');
      if (icon) {
        icon.style.transform = 'scale(1.2) rotate(10deg)';
      }
    });
    
    card.addEventListener('mouseleave', () => {
      const icon = card.querySelector('i');
      if (icon) {
        icon.style.transform = 'scale(1)';
      }
    });
  });
  
  // Add hover effect to sidebar links
  const navLinks = document.querySelectorAll('.sidebar .nav-link');
  navLinks.forEach(link => {
    link.addEventListener('mouseenter', () => {
      const icon = link.querySelector('i');
      if (icon) {
        icon.style.transform = 'translateX(3px)';
      }
    });
    
    link.addEventListener('mouseleave', () => {
      const icon = link.querySelector('i');
      if (icon) {
        icon.style.transform = 'translateX(0)';
      }
    });
  });
}

/**
 * Observes changes to the log list and animates new entries
 */
function observeLogChanges() {
  const logList = document.getElementById('logList');
  if (!logList) return;
  
  // Create a MutationObserver to watch for new log entries
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === 1 && node.classList.contains('log-entry')) {
            // Add animation class to new log entry
            node.classList.add('fade-in');
            
            // Scroll to the bottom of the logs panel
            const logsPanel = document.querySelector('.logs-panel');
            if (logsPanel) {
              logsPanel.scrollTop = logsPanel.scrollHeight;
            }
          }
        });
      }
    });
  });
  
  // Start observing the log list
  observer.observe(logList, { childList: true });
}

/**
 * Updates the status badge with animation
 */
function updateStatusWithAnimation(status) {
  const statusBadge = document.getElementById('status-badge');
  if (!statusBadge) return;
  
  // Remove existing classes
  statusBadge.classList.remove('bg-success', 'bg-danger', 'bg-warning', 'pulse-animation');
  
  // Add appropriate class based on status
  if (status === 'Running') {
    statusBadge.classList.add('bg-success', 'pulse-animation');
  } else if (status === 'Stopped') {
    statusBadge.classList.add('bg-danger');
  } else {
    statusBadge.classList.add('bg-warning');
  }
  
  // Update text
  statusBadge.textContent = status;
}

// Override the updateStatus function to include animation
const originalUpdateStatus = window.updateStatus;
if (typeof originalUpdateStatus === 'function') {
  window.updateStatus = function() {
    originalUpdateStatus();
    
    // Get the current status from the badge
    const statusBadge = document.getElementById('status-badge');
    if (statusBadge) {
      const status = statusBadge.textContent;
      updateStatusWithAnimation(status);
    }
  };
}
