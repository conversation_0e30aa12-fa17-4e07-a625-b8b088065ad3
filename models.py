from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
import json

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.String(100), primary_key=True)  # Google sub ID
    email = db.Column(db.String(100), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    picture = db.Column(db.Text)  # Changed from String(200) to Text to handle long URLs
    is_admin = db.Column(db.Boolean, default=False)  # Admin role flag
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    oauth_token = db.relationship('OAuthToken', backref='user', uselist=False)
    user_settings = db.relationship('UserSettings', backref='user', uselist=False)
    user_logs = db.relationship('UserLog', backref='user')
    automated_emails = db.relationship('AutomatedEmail', backref='user', cascade='all, delete-orphan')
    email_replies = db.relationship('EmailReply', backref='user', cascade='all, delete-orphan')
    user_stats = db.relationship('UserStats', backref='user', uselist=False)

    def __init__(self, id, email, name, picture, is_admin=False):
        self.id = id
        self.email = email
        self.name = name
        self.picture = picture
        self.is_admin = is_admin

    @staticmethod
    def get(user_id):
        return User.query.get(user_id)

    def is_administrator(self):
        """Check if the user has admin privileges"""
        return self.is_admin

    @staticmethod
    def create_or_update(google_id, email, name, picture, token_info):
        user = User.query.get(google_id)
        if user:
            user.email = email
            user.name = name
            user.picture = picture
        else:
            user = User(google_id, email, name, picture)
            db.session.add(user)
        db.session.commit()

        oauth_token = OAuthToken.query.get(google_id)
        if oauth_token:
            oauth_token.update_token(token_info)
        else:
            oauth_token = OAuthToken(google_id, token_info)
            db.session.add(oauth_token)
            db.session.commit()

        user_settings = UserSettings.query.get(google_id)
        if not user_settings:
            user_settings = UserSettings(google_id)
            db.session.add(user_settings)
            db.session.commit()

        # Create user stats if they don't exist
        user_stats = UserStats.query.filter_by(user_id=google_id).first()
        if not user_stats:
            user_stats = UserStats(google_id)
            db.session.add(user_stats)
            db.session.commit()

        return user

    @staticmethod
    def set_admin_status(user_id, is_admin=True):
        """Set a user as an admin or remove admin privileges"""
        user = User.query.get(user_id)
        if user:
            user.is_admin = is_admin
            db.session.commit()
            return True
        return False

    @staticmethod
    def get_token(user_id):
        try:
            # Get the OAuth token from the database
            oauth_token = OAuthToken.query.filter_by(user_id=user_id).first()
            if not oauth_token:
                print(f"No OAuth token found for user {user_id}")
                return None

            # Get the credentials from the token
            try:
                credentials = oauth_token.get_credentials()
                return credentials
            except Exception as e:
                print(f"Error getting credentials from token for user {user_id}: {e}")
                # Try to fix the token if it's a string
                if isinstance(oauth_token.token_info, str):
                    try:
                        import json
                        token_info = json.loads(oauth_token.token_info)
                        return token_info
                    except Exception as e2:
                        print(f"Error parsing token_info as JSON: {e2}")
                return None
        except Exception as e:
            print(f"Error in get_token for user {user_id}: {e}")
            return None

    @staticmethod
    def log_action(user_id, message):
        try:
            # Print the message to console first
            print(f"[{datetime.now()}] {message}")

            # Then try to save to database
            user_log = UserLog(user_id, message)
            db.session.add(user_log)
            db.session.commit()
        except Exception as e:
            print(f"Error logging to database: {e}")

    @staticmethod
    def get_logs(user_id, limit=50):
        return [{'message': log.message, 'timestamp': log.created_at} for log in UserLog.query.filter_by(user_id=user_id).order_by(UserLog.created_at.desc()).limit(limit).all()]

    @staticmethod
    def get_settings(user_id):
        try:
            # Use filter_by instead of get since user_id is not the primary key
            user_settings = UserSettings.query.filter_by(user_id=user_id).first()
            if user_settings:
                print(f"Found settings for user {user_id}: agent_enabled={user_settings.agent_enabled}")
                return {
                    'agent_enabled': user_settings.agent_enabled,
                    'check_interval': user_settings.check_interval,
                    'custom_signature': user_settings.custom_signature
                }
            # Try to create default settings
            print(f"No settings found for user {user_id}, creating default settings")
            # Check if user exists
            user = User.query.get(user_id)
            if user:
                # Create default settings
                user_settings = UserSettings(user_id)
                user_settings.agent_enabled = True  # Enable by default
                db.session.add(user_settings)
                db.session.commit()
                print(f"Created default settings for user {user_id}")
                return {
                    'agent_enabled': user_settings.agent_enabled,
                    'check_interval': user_settings.check_interval,
                    'custom_signature': user_settings.custom_signature
                }
            else:
                print(f"User {user_id} not found in database")
        except Exception as e:
            print(f"Error in get_settings for user {user_id}: {e}")
        return None

    @staticmethod
    def update_settings(user_id, settings):
        try:
            # Use filter_by instead of get since user_id is not the primary key
            user_settings = UserSettings.query.filter_by(user_id=user_id).first()
            if user_settings:
                print(f"Updating settings for user {user_id}: agent_enabled={settings.get('agent_enabled', False)}")
                user_settings.agent_enabled = settings.get('agent_enabled', False)
                user_settings.check_interval = settings.get('check_interval', 10)
                user_settings.custom_signature = settings.get('custom_signature', '')
                db.session.commit()
                print(f"Settings updated for user {user_id}")
            else:
                # Create new settings
                print(f"No settings found for user {user_id}, creating new settings")
                # Check if user exists
                user = User.query.get(user_id)
                if user:
                    # Create new settings
                    user_settings = UserSettings(user_id)
                    user_settings.agent_enabled = settings.get('agent_enabled', False)
                    user_settings.check_interval = settings.get('check_interval', 10)
                    user_settings.custom_signature = settings.get('custom_signature', '')
                    db.session.add(user_settings)
                    db.session.commit()
                    print(f"Created new settings for user {user_id}")
                else:
                    print(f"User {user_id} not found in database")
        except Exception as e:
            print(f"Error in update_settings for user {user_id}: {e}")

    @staticmethod
    def update_token(user_id, token_info):
        """Update the OAuth token for a user"""
        oauth_token = OAuthToken.query.filter_by(user_id=user_id).first()
        if oauth_token:
            oauth_token.update_token(token_info)
        else:
            oauth_token = OAuthToken(user_id, token_info)
            db.session.add(oauth_token)
            db.session.commit()

class OAuthToken(db.Model):
    user_id = db.Column(db.String(100), db.ForeignKey('user.id'), primary_key=True)
    token_info = db.Column(db.Text, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_credentials(self):
        try:
            # If token_info is already a dictionary, return it directly
            if isinstance(self.token_info, dict):
                return self.token_info

            # Otherwise, try to parse it as JSON
            return json.loads(self.token_info)
        except Exception as e:
            print(f"Error parsing token_info: {e}")
            # If token_info is a string but not valid JSON, return it as is
            if isinstance(self.token_info, str):
                return self.token_info
            return None

    def update_token(self, token_info):
        self.token_info = json.dumps(token_info)
        db.session.commit()

    def __init__(self, user_id, token_info):
        self.user_id = user_id
        self.token_info = json.dumps(token_info)

class UserSettings(db.Model):
    user_id = db.Column(db.String(100), db.ForeignKey('user.id'), primary_key=True)
    agent_enabled = db.Column(db.Boolean, default=False)
    check_interval = db.Column(db.Integer, default=10)
    custom_signature = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, user_id):
        self.user_id = user_id

class UserLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(100), db.ForeignKey('user.id'), nullable=False)
    message = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __init__(self, user_id, message):
        self.user_id = user_id
        self.message = message

class AutomatedEmail(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(100), db.ForeignKey('user.id'), nullable=False)
    email_address = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __init__(self, user_id, email_address):
        self.user_id = user_id
        self.email_address = email_address

    @staticmethod
    def get_user_automated_emails(user_id):
        """Get all automated email addresses for a user"""
        return [email.email_address for email in AutomatedEmail.query.filter_by(user_id=user_id).all()]

    @staticmethod
    def add_automated_email(user_id, email_address):
        """Add a new automated email address for a user"""
        # Check if email already exists for this user
        existing = AutomatedEmail.query.filter_by(user_id=user_id, email_address=email_address).first()
        if existing:
            return False

        new_email = AutomatedEmail(user_id, email_address)
        db.session.add(new_email)
        db.session.commit()
        return True

    @staticmethod
    def remove_automated_email(user_id, email_address):
        """Remove an automated email address for a user"""
        email = AutomatedEmail.query.filter_by(user_id=user_id, email_address=email_address).first()
        if email:
            db.session.delete(email)
            db.session.commit()
            return True
        return False

class EmailReply(db.Model):
    """Model to store email replies before they are sent"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(100), db.ForeignKey('user.id'), nullable=False)
    email_id = db.Column(db.String(100), nullable=False)  # Gmail message ID
    sender = db.Column(db.String(255), nullable=False)    # Original sender
    recipient = db.Column(db.String(255), nullable=False) # Who we're replying to
    subject = db.Column(db.String(255), nullable=False)   # Email subject
    original_body = db.Column(db.Text, nullable=False)    # Original email body
    reply_body = db.Column(db.Text, nullable=False)       # Generated reply
    modified_reply = db.Column(db.Text)                   # User-modified reply
    status = db.Column(db.String(50), default='pending')  # pending, sent, discarded
    tone = db.Column(db.String(50), default='professional') # Tone parameter
    length = db.Column(db.String(50), default='medium')   # Length parameter
    custom_instructions = db.Column(db.Text)              # Custom instructions
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, user_id, email_id, sender, recipient, subject, original_body, reply_body,
                 tone='professional', length='medium', custom_instructions=None):
        self.user_id = user_id
        self.email_id = email_id
        self.sender = sender
        self.recipient = recipient
        self.subject = subject
        self.original_body = original_body
        self.reply_body = reply_body
        self.tone = tone
        self.length = length
        self.custom_instructions = custom_instructions

    @staticmethod
    def get_pending_replies(user_id):
        """Get all pending email replies for a user"""
        return EmailReply.query.filter_by(user_id=user_id, status='pending').all()

    @staticmethod
    def get_reply(reply_id, user_id=None):
        """Get a specific email reply"""
        if user_id:
            return EmailReply.query.filter_by(id=reply_id, user_id=user_id).first()
        return EmailReply.query.get(reply_id)

    @staticmethod
    def create_reply(user_id, email_id, sender, recipient, subject, original_body, reply_body,
                     tone='professional', length='medium', custom_instructions=None):
        """Create a new email reply"""
        reply = EmailReply(user_id, email_id, sender, recipient, subject, original_body, reply_body,
                          tone, length, custom_instructions)
        db.session.add(reply)
        db.session.commit()
        return reply

    @staticmethod
    def update_reply(reply_id, user_id, **kwargs):
        """Update an email reply"""
        reply = EmailReply.query.filter_by(id=reply_id, user_id=user_id).first()
        if reply:
            for key, value in kwargs.items():
                if hasattr(reply, key):
                    setattr(reply, key, value)
            db.session.commit()
            return reply
        return None

    @staticmethod
    def mark_as_sent(reply_id, user_id):
        """Mark an email reply as sent"""
        reply = EmailReply.query.filter_by(id=reply_id, user_id=user_id).first()
        if reply:
            reply.status = 'sent'
            db.session.commit()
            return True
        return False

    @staticmethod
    def discard_reply(reply_id, user_id):
        """Discard an email reply"""
        reply = EmailReply.query.filter_by(id=reply_id, user_id=user_id).first()
        if reply:
            reply.status = 'discarded'
            db.session.commit()
            return True
        return False

    @staticmethod
    def get_reply_by_email_id(user_id, email_id):
        """Check if a reply already exists for a specific email ID"""
        return EmailReply.query.filter_by(user_id=user_id, email_id=email_id).first()

    @staticmethod
    def discard_all_pending_replies(user_id):
        """Discard all pending email replies for a user"""
        pending_replies = EmailReply.query.filter_by(user_id=user_id, status='pending').all()
        count = 0
        for reply in pending_replies:
            reply.status = 'discarded'
            count += 1
        db.session.commit()
        return count

class UserStats(db.Model):
    """Model to store user statistics"""
    user_id = db.Column(db.String(100), db.ForeignKey('user.id'), primary_key=True)
    emails_processed = db.Column(db.Integer, default=0)
    replies_sent = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, user_id):
        self.user_id = user_id

    @staticmethod
    def get_stats(user_id):
        """Get stats for a user"""
        stats = UserStats.query.filter_by(user_id=user_id).first()
        if not stats:
            # Create default stats
            stats = UserStats(user_id)
            db.session.add(stats)
            db.session.commit()
        return {
            'emails_processed': stats.emails_processed,
            'replies_sent': stats.replies_sent
        }

    @staticmethod
    def increment_emails_processed(user_id, count=1):
        """Increment the emails processed count"""
        stats = UserStats.query.filter_by(user_id=user_id).first()
        if not stats:
            stats = UserStats(user_id)
            db.session.add(stats)
        stats.emails_processed += count
        db.session.commit()
        return stats.emails_processed

    @staticmethod
    def increment_replies_sent(user_id, count=1):
        """Increment the replies sent count"""
        stats = UserStats.query.filter_by(user_id=user_id).first()
        if not stats:
            stats = UserStats(user_id)
            db.session.add(stats)
        stats.replies_sent += count
        db.session.commit()
        return stats.replies_sent
