<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    {% set seo_data = seo or get_seo_data('default') %}
    <title>{{ seo_data.title }}</title>
    <meta name="description" content="{{ seo_data.description }}" />
    <meta name="keywords" content="{{ seo_data.keywords }}" />
    <meta name="author" content="Email Reply Agent" />
    <meta name="robots" content="index, follow" />
    <link
      rel="canonical"
      href="{{ get_canonical_url(request.endpoint, **request.view_args) if request.endpoint else seo_data.base_url }}"
    />

    <!-- Open Graph Protocol -->
    <meta
      property="og:title"
      content="{{ seo_data.og_title or seo_data.title }}"
    />
    <meta
      property="og:description"
      content="{{ seo_data.og_description or seo_data.description }}"
    />
    <meta property="og:image" content="{{ seo_data.og_image }}" />
    <meta
      property="og:url"
      content="{{ get_canonical_url(request.endpoint, **request.view_args) if request.endpoint else seo_data.base_url }}"
    />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="{{ seo_data.site_name }}" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter Cards -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="{{ seo_data.twitter_site }}" />
    <meta
      name="twitter:title"
      content="{{ seo_data.twitter_title or seo_data.title }}"
    />
    <meta
      name="twitter:description"
      content="{{ seo_data.twitter_description or seo_data.description }}"
    />
    <meta
      name="twitter:image"
      content="{{ seo_data.twitter_image or seo_data.og_image }}"
    />

    <!-- Enhanced Favicon Implementation -->
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="{{ url_for('static', filename='img/favicon-16x16.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="{{ url_for('static', filename='img/favicon-32x32.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="48x48"
      href="{{ url_for('static', filename='img/favicon-48x48.png') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="{{ url_for('static', filename='img/apple-touch-icon.png') }}"
    />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='manifest.json') }}"
    />
    <meta name="theme-color" content="#1E90FF" />

    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Email Reply Agent",
        "description": "AI-powered email automation tool for Gmail users",
        "url": "{{ seo_data.base_url }}",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD"
        },
        "creator": {
          "@type": "Organization",
          "name": "Email Reply Agent"
        }
      }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/styles.css') }}"
    />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
      <div class="container">
        <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
          <i class="fas fa-envelope-open-text text-primary me-2"></i>
          Email Reply Agent
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            {% if current_user.is_authenticated %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('dashboard') }}"
                >Dashboard</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('profile') }}">Profile</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('settings') }}">Settings</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('logout') }}">Logout</a>
            </li>
            {% else %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('login') }}">Login</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('signup') }}">Sign Up</a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    {% with messages = get_flashed_messages(with_categories=true) %} {% if
    messages %} {% for category, message in messages %}
    <div
      class="alert alert-{{ category }} alert-dismissible fade show"
      role="alert"
    >
      {{ message }}
      <button
        type="button"
        class="btn-close"
        data-bs-dismiss="alert"
        aria-label="Close"
      ></button>
    </div>
    {% endfor %} {% endif %} {% endwith %} {% block content %}{% endblock %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    {% block scripts %}{% endblock %}
  </body>
</html>
