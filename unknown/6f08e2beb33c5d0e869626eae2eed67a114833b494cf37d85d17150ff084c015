<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Favicon -->
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />
    <title>{% block title %}Email Agent{% endblock %}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/styles.css') }}"
    />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
      <div class="container">
        <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
          <i class="fas fa-envelope-open-text text-primary me-2"></i>
          Email Reply Agent
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            {% if current_user.is_authenticated %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('dashboard') }}"
                >Dashboard</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('profile') }}">Profile</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('settings') }}">Settings</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('logout') }}">Logout</a>
            </li>
            {% else %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('login') }}">Login</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('signup') }}">Sign Up</a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    {% with messages = get_flashed_messages(with_categories=true) %} {% if
    messages %} {% for category, message in messages %}
    <div
      class="alert alert-{{ category }} alert-dismissible fade show"
      role="alert"
    >
      {{ message }}
      <button
        type="button"
        class="btn-close"
        data-bs-dismiss="alert"
        aria-label="Close"
      ></button>
    </div>
    {% endfor %} {% endif %} {% endwith %} {% block content %}{% endblock %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    {% block scripts %}{% endblock %}
  </body>
</html>
