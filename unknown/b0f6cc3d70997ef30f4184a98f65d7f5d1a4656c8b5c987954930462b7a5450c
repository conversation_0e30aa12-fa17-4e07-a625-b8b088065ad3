<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="twitterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#1E90FF;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#0066CC;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#004499;stop-opacity:1" />
        </linearGradient>
    </defs>
    
    <!-- Background -->
    <rect width="1200" height="630" fill="url(#twitterGradient)"/>
    
    <!-- Central Email Icon -->
    <rect x="400" y="180" width="400" height="270" rx="25" fill="white" opacity="0.95"/>
    <path d="M400 230 L600 350 L800 230" stroke="#1E90FF" stroke-width="10" fill="none"/>
    
    <!-- Title -->
    <text x="600" y="120" font-family="Arial, sans-serif" font-size="42" font-weight="bold" fill="white" text-anchor="middle">
        Email Reply Agent
    </text>
    
    <!-- Subtitle -->
    <text x="600" y="520" font-family="Arial, sans-serif" font-size="28" fill="white" text-anchor="middle" opacity="0.9">
        AI-Powered Email Automation for Gmail
    </text>
    
    <!-- Decorative elements -->
    <circle cx="200" cy="150" r="25" fill="white" opacity="0.3"/>
    <circle cx="1000" cy="480" r="30" fill="white" opacity="0.25"/>
    <circle cx="150" cy="450" r="20" fill="white" opacity="0.2"/>
    <circle cx="1050" cy="180" r="15" fill="white" opacity="0.3"/>
</svg>
