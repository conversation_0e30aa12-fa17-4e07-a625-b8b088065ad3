<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    {% set seo_data = seo or get_seo_data('login') %}
    <title>{{ seo_data.title }}</title>
    <meta name="description" content="{{ seo_data.description }}" />
    <meta name="keywords" content="{{ seo_data.keywords }}" />
    <meta name="author" content="Email Reply Agent" />
    <meta name="robots" content="index, follow" />
    <link rel="canonical" href="{{ get_canonical_url('login') }}" />

    <!-- Open Graph Protocol -->
    <meta property="og:title" content="{{ seo_data.og_title }}" />
    <meta property="og:description" content="{{ seo_data.og_description }}" />
    <meta property="og:image" content="{{ seo_data.og_image }}" />
    <meta property="og:url" content="{{ get_canonical_url('login') }}" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="{{ seo_data.site_name }}" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter Cards -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="{{ seo_data.twitter_site }}" />
    <meta name="twitter:title" content="{{ seo_data.twitter_title }}" />
    <meta
      name="twitter:description"
      content="{{ seo_data.twitter_description }}"
    />
    <meta name="twitter:image" content="{{ seo_data.twitter_image }}" />

    <!-- Enhanced Favicon Implementation -->
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="{{ url_for('static', filename='img/favicon-16x16.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="{{ url_for('static', filename='img/favicon-32x32.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="48x48"
      href="{{ url_for('static', filename='img/favicon-48x48.png') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="{{ url_for('static', filename='img/apple-touch-icon.png') }}"
    />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='manifest.json') }}"
    />
    <meta name="theme-color" content="#1E90FF" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/styles.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/auth.css') }}"
    />
  </head>
  <body class="auth-page">
    <!-- Navbar with enhanced styling -->
    <nav class="auth-navbar">
      <div class="container">
        <a class="navbar-brand" href="{{ url_for('index') }}">
          <i class="bi bi-envelope-paper-heart"></i>
          <span>Email Reply Agent</span>
        </a>
        <a href="{{ url_for('index') }}" class="btn-home">
          <i class="bi bi-house-door"></i>Back to Home
        </a>
      </div>
    </nav>

    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-card-header text-center">
          <div class="auth-logo">
            <i class="bi bi-envelope-paper-heart"></i>
          </div>
          <h2 class="auth-title">Welcome Back!</h2>
          <p class="auth-subtitle">
            Sign in to your account to manage your email automation settings
          </p>
        </div>
        <div class="auth-card-body">
          <a href="{{ url_for('auth') }}" class="btn-google">
            <img
              src="{{ url_for('static', filename='img/google-logo.png') }}"
              alt="Google"
              class="google-icon"
            />
            Sign in with Google
          </a>
          <div class="account-toggle">
            <p>
              Don't have an account?
              <a href="{{ url_for('signup') }}">Sign up</a>
            </p>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/auth.js') }}"></script>
  </body>
</html>
