/**
 * Email Reply Agent - Dashboard JavaScript
 */

document.addEventListener("DOMContentLoaded", () => {
  // DOM Elements
  const startBtn = document.getElementById("startBtn");
  const stopBtn = document.getElementById("stopBtn");
  const statusBadge = document.getElementById("status-badge");
  const logList = document.getElementById("logList");

  // Fetch and update agent status
  function updateStatus() {
    fetch("/status")
      .then((response) => response.json())
      .then((data) => {
        if (data.running) {
          statusBadge.textContent = "Running";
          statusBadge.className = "badge rounded-pill bg-success";
          startBtn.disabled = true;
          stopBtn.disabled = false;
        } else {
          statusBadge.textContent = "Stopped";
          statusBadge.className = "badge rounded-pill bg-danger";
          startBtn.disabled = false;
          stopBtn.disabled = true;
        }
      })
      .catch((error) => {
        console.error("Error fetching status:", error);
        statusBadge.textContent = "Error";
        statusBadge.className = "badge rounded-pill bg-warning";
      });
  }

  // Fetch and update logs
  function updateLogs() {
    fetch("/logs")
      .then((response) => response.json())
      .then((data) => {
        logList.innerHTML = ""; // Clear existing logs

        if (data.logs.length === 0) {
          const emptyLogItem = document.createElement("div");
          emptyLogItem.className = "log-item";
          emptyLogItem.textContent = "No logs available yet.";
          logList.appendChild(emptyLogItem);
        } else {
          data.logs.forEach((log) => {
            const logItem = document.createElement("div");
            logItem.className = "log-item";
            logItem.textContent = log;
            logList.appendChild(logItem);
          });
        }

        // Auto-scroll to bottom
        const logBox = document.getElementById("logBox");
        logBox.scrollTop = logBox.scrollHeight;
      })
      .catch((error) => console.error("Error fetching logs:", error));
  }

  // Start agent
  startBtn.addEventListener("click", () => {
    startBtn.disabled = true; // Prevent multiple clicks

    fetch("/start", { method: "POST" })
      .then((response) => response.json())
      .then((data) => {
        // Show toast or notification instead of alert
        const message = data.status;
        console.log(message);

        // Update UI
        updateStatus();
        updateLogs();
      })
      .catch((error) => {
        console.error("Error starting agent:", error);
        startBtn.disabled = false;
      });
  });

  // Stop agent
  stopBtn.addEventListener("click", () => {
    stopBtn.disabled = true; // Prevent multiple clicks

    fetch("/stop", { method: "POST" })
      .then((response) => response.json())
      .then((data) => {
        // Show toast or notification instead of alert
        const message = data.status;
        console.log(message);

        // Update UI
        updateStatus();
        updateLogs();
      })
      .catch((error) => {
        console.error("Error stopping agent:", error);
        stopBtn.disabled = false;
      });
  });

  // Initial updates
  updateStatus();
  updateLogs();

  // Periodic updates
  setInterval(updateStatus, 5000); // Status every 5 seconds
  setInterval(updateLogs, 5000); // Logs every 5 seconds
});
