// Modern Landing Page JavaScript

// Wait for the DOM to be fully loaded
document.addEventListener("DOMContentLoaded", function () {
  // Mobile Menu Toggle
  const mobileMenu = document.getElementById("mobile-menu");
  const navMenu = document.querySelector(".nav-menu");

  if (mobileMenu) {
    mobileMenu.addEventListener("click", function () {
      mobileMenu.classList.toggle("active");
      navMenu.classList.toggle("active");

      // Toggle hamburger animation
      const bars = mobileMenu.querySelectorAll(".bar");
      if (mobileMenu.classList.contains("active")) {
        bars[0].style.transform = "rotate(-45deg) translate(-5px, 6px)";
        bars[1].style.opacity = "0";
        bars[2].style.transform = "rotate(45deg) translate(-5px, -6px)";
      } else {
        bars[0].style.transform = "none";
        bars[1].style.opacity = "1";
        bars[2].style.transform = "none";
      }
    });
  }

  // Close mobile menu when clicking a nav link
  const navLinks = document.querySelectorAll(".nav-link");
  navLinks.forEach((link) => {
    link.addEventListener("click", function () {
      if (navMenu.classList.contains("active")) {
        mobileMenu.click();
      }
    });
  });

  // Sticky Header on Scroll
  const header = document.querySelector(".header");
  let scrollPosition = 0;

  window.addEventListener("scroll", function () {
    const currentPosition = window.pageYOffset;

    if (currentPosition > 100) {
      header.classList.add("scrolled");
    } else {
      header.classList.remove("scrolled");
    }

    scrollPosition = currentPosition;
  });

  // Smooth Scrolling for Navigation Links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();

      const targetId = this.getAttribute("href");
      if (targetId === "#") return;

      const targetElement = document.querySelector(targetId);
      if (targetElement) {
        const headerHeight = header.offsetHeight;
        const targetPosition =
          targetElement.getBoundingClientRect().top +
          window.pageYOffset -
          headerHeight;

        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    });
  });

  // FAQ Accordion
  const faqItems = document.querySelectorAll(".faq-item");

  faqItems.forEach((item) => {
    const question = item.querySelector(".faq-question");

    question.addEventListener("click", function () {
      // Close all other items
      faqItems.forEach((otherItem) => {
        if (otherItem !== item && otherItem.classList.contains("active")) {
          otherItem.classList.remove("active");
        }
      });

      // Toggle current item
      item.classList.toggle("active");
    });
  });

  // Activate first FAQ item by default
  if (faqItems.length > 0) {
    faqItems[0].classList.add("active");
  }

  // Testimonial Slider Dots
  const testimonialDots = document.querySelectorAll(".testimonial-dots .dot");
  const testimonialSlider = document.querySelector(".testimonials-slider");

  if (testimonialDots.length > 0 && testimonialSlider) {
    testimonialDots.forEach((dot, index) => {
      dot.addEventListener("click", function () {
        // Remove active class from all dots
        testimonialDots.forEach((d) => d.classList.remove("active"));

        // Add active class to current dot
        this.classList.add("active");

        // Calculate scroll position
        const testimonialCards = document.querySelectorAll(".testimonial-card");
        if (testimonialCards.length > 0) {
          const cardWidth = testimonialCards[0].offsetWidth;
          const gap = parseInt(
            window.getComputedStyle(testimonialSlider).getPropertyValue("gap")
          );
          const scrollPosition = index * (cardWidth + gap);

          testimonialSlider.scrollTo({
            left: scrollPosition,
            behavior: "smooth",
          });
        }
      });
    });
  }

  // Handle Newsletter Form Submission
  const newsletterForm = document.querySelector(".newsletter-form");
  if (newsletterForm) {
    newsletterForm.addEventListener("submit", function (e) {
      e.preventDefault();

      const emailInput = this.querySelector('input[type="email"]');
      const email = emailInput.value.trim();

      if (email) {
        // Show success message (in a real app, you'd send this to your backend)
        alert("Thank you for subscribing to our newsletter!");
        emailInput.value = "";
      }
    });
  }

  // Enhanced animate elements on scroll with border effects
  const animateOnScroll = function () {
    const elements = document.querySelectorAll(
      ".feature-card, .step, .testimonial-card, .pricing-card"
    );

    elements.forEach((element) => {
      const elementPosition = element.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;

      if (elementPosition < windowHeight - 100) {
        element.style.opacity = "1";

        // Apply different transforms based on element type
        if (
          element.classList.contains("pricing-card") &&
          element.classList.contains("featured")
        ) {
          element.style.transform = "scale(1.05)";
        } else if (element.classList.contains("testimonial-card")) {
          element.style.transform = "translateX(0)";
        } else {
          element.style.transform = "translateY(0)";
        }

        // Add subtle border animation for visible elements
        if (
          !element.classList.contains("border-animated") &&
          (element.classList.contains("feature-card") ||
            element.classList.contains("testimonial-card"))
        ) {
          element.classList.add("border-animated");
        }
      }
    });
  };

  // Set initial opacity for animated elements
  document
    .querySelectorAll(".feature-card, .step, .testimonial-card, .pricing-card")
    .forEach((element) => {
      element.style.opacity = "0";

      if (element.classList.contains("testimonial-card")) {
        element.style.transform = "translateX(20px)";
      } else {
        element.style.transform = "translateY(20px)";
      }

      element.style.transition =
        "opacity 0.6s ease, transform 0.6s ease, border-color 0.6s ease";
    });

  // Run animation on page load and scroll
  window.addEventListener("load", animateOnScroll);
  window.addEventListener("scroll", animateOnScroll);

  // Enhanced Professional Carousel functionality
  const carousel = {
    container: document.querySelector(".carousel-container"),
    deviceFrame: document.querySelector(".carousel-device-frame"),
    slides: document.querySelectorAll(".carousel-slide"),
    indicators: document.querySelectorAll(".carousel-indicator"),
    thumbnails: document.querySelectorAll(".carousel-thumbnail"),
    deviceTitle: document.querySelector(".carousel-device-title"),
    prevBtn: document.querySelector(".carousel-prev"),
    nextBtn: document.querySelector(".carousel-next"),
    currentIndex: 0,
    interval: null,
    isHovering: false,
    touchStartX: 0,
    touchEndX: 0,

    init: function () {
      if (!this.container) return;

      // Set up button event listeners
      this.prevBtn.addEventListener("click", (e) => {
        e.preventDefault();
        this.prevSlide();
        this.resetAutoplayTimer();
      });

      this.nextBtn.addEventListener("click", (e) => {
        e.preventDefault();
        this.nextSlide();
        this.resetAutoplayTimer();
      });

      // Set up indicator clicks
      this.indicators.forEach((indicator, index) => {
        indicator.addEventListener("click", () => {
          this.goToSlide(index);
          this.resetAutoplayTimer();
        });
      });

      // Set up thumbnail clicks and hover
      this.thumbnails.forEach((thumbnail, index) => {
        // Click event
        thumbnail.addEventListener("click", () => {
          this.goToSlide(index);
          this.resetAutoplayTimer();
        });

        // Hover event for desktop
        thumbnail.addEventListener("mouseenter", () => {
          if (window.innerWidth > 768) {
            this.isHovering = true;
            this.goToSlide(index);
          }
        });

        thumbnail.addEventListener("mouseleave", () => {
          this.isHovering = false;
        });
      });

      // Touch events for mobile swipe
      this.deviceFrame.addEventListener("touchstart", (e) => {
        this.touchStartX = e.changedTouches[0].screenX;
      });

      this.deviceFrame.addEventListener("touchend", (e) => {
        this.touchEndX = e.changedTouches[0].screenX;
        this.handleSwipe();
      });

      // Start autoplay
      this.startAutoplay();

      // Pause autoplay on hover over the device frame
      this.deviceFrame.addEventListener("mouseenter", () =>
        this.stopAutoplay()
      );
      this.deviceFrame.addEventListener("mouseleave", () => {
        if (!this.isHovering) {
          this.startAutoplay();
        }
      });

      // Update device title based on active slide
      this.updateDeviceTitle();

      // Add keyboard navigation
      document.addEventListener("keydown", (e) => {
        if (this.isElementInViewport(this.container)) {
          if (e.key === "ArrowLeft") {
            this.prevSlide();
            this.resetAutoplayTimer();
          } else if (e.key === "ArrowRight") {
            this.nextSlide();
            this.resetAutoplayTimer();
          }
        }
      });
    },

    goToSlide: function (index) {
      // Remove active class from current slide, indicator and thumbnail
      this.slides[this.currentIndex].classList.remove("active");
      this.indicators[this.currentIndex].classList.remove("active");
      this.thumbnails[this.currentIndex].classList.remove("active");

      // Update current index
      this.currentIndex = index;

      // Handle index bounds
      if (this.currentIndex < 0) {
        this.currentIndex = this.slides.length - 1;
      } else if (this.currentIndex >= this.slides.length) {
        this.currentIndex = 0;
      }

      // Add active class to new slide, indicator and thumbnail
      this.slides[this.currentIndex].classList.add("active");
      this.indicators[this.currentIndex].classList.add("active");
      this.thumbnails[this.currentIndex].classList.add("active");

      // Update device title
      this.updateDeviceTitle();
    },

    nextSlide: function () {
      this.goToSlide(this.currentIndex + 1);
    },

    prevSlide: function () {
      this.goToSlide(this.currentIndex - 1);
    },

    handleSwipe: function () {
      const swipeThreshold = 50;
      if (this.touchEndX < this.touchStartX - swipeThreshold) {
        // Swipe left, go to next slide
        this.nextSlide();
        this.resetAutoplayTimer();
      } else if (this.touchEndX > this.touchStartX + swipeThreshold) {
        // Swipe right, go to previous slide
        this.prevSlide();
        this.resetAutoplayTimer();
      }
    },

    updateDeviceTitle: function () {
      if (this.deviceTitle && this.slides[this.currentIndex]) {
        const title = this.slides[this.currentIndex].getAttribute("data-title");
        this.deviceTitle.textContent = `Email Agent - ${title}`;
      }
    },

    startAutoplay: function () {
      if (this.isHovering) return;
      this.stopAutoplay(); // Clear any existing interval
      this.interval = setInterval(() => {
        if (!this.isHovering) {
          this.nextSlide();
        }
      }, 5000); // Change slide every 5 seconds
    },

    stopAutoplay: function () {
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
    },

    resetAutoplayTimer: function () {
      this.stopAutoplay();
      this.startAutoplay();
    },

    isElementInViewport: function (el) {
      const rect = el.getBoundingClientRect();
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <=
          (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <=
          (window.innerWidth || document.documentElement.clientWidth)
      );
    },
  };

  // Initialize enhanced carousel
  carousel.init();
});
