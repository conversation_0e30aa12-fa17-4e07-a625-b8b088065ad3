/* Auth Pages Enhanced CSS - Aligned with Landing Page Design */
:root {
  /* Color Palette - Matching Landing Page */
  --primary: #077a7d; /* Teal - main brand color */
  --primary-dark: #06595b; /* Darker teal for contrast */
  --primary-light: #2daa9e; /* Lighter teal for accents */
  --secondary: #7ae2cf; /* Mint green - complementary to teal */
  --secondary-dark: #5ecfbc; /* Darker mint green */
  --accent: #f5eedd; /* Cream for backgrounds and accents */
  --accent-dark: #e3d2c3; /* Darker cream/beige */
  --light: #eaeaea; /* Light gray for backgrounds */
  --dark: #06202b; /* Dark navy for text - better contrast */
  --white: #ffffff;
  --gray: #495057; /* Darker gray for better readability */
  --gray-light: #e9ecef;
  --success: #2daa9e;
  --danger: #dc3545;
  --warning: #ffc107;
  --info: #077a7d;

  /* Gradients */
  --gradient-bg: linear-gradient(135deg, #06202b 0%, #077a7d 100%);
  --gradient-btn-primary: linear-gradient(135deg, #077a7d 0%, #06595b 100%);
  --gradient-btn-hover: linear-gradient(135deg, #06595b 0%, #077a7d 100%);
  --gradient-card: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(245, 238, 221, 0.8) 100%
  );
  --gradient-title: linear-gradient(
    to right,
    #ff4d4d 0%,
    #ff9900 30%,
    #7ae2cf 60%,
    #077a7d 100%
  );

  /* Typography */
  --font-primary: "Montserrat", sans-serif;
  --font-secondary: "Poppins", sans-serif;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 2rem;
  --spacing-lg: 4rem;

  /* Borders & Shadows */
  --border-radius-sm: 6px;
  --border-radius-md: 12px;
  --border-radius-lg: 20px;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --box-shadow-hover: 0 15px 35px rgba(0, 0, 0, 0.15);
  --box-shadow-btn: 0 4px 10px rgba(7, 122, 125, 0.25);
  --box-shadow-btn-hover: 0 8px 20px rgba(7, 122, 125, 0.35);

  /* Transitions */
  --transition-fast: 0.3s ease;
  --transition-medium: 0.5s ease;
  --transition-slow: 0.8s ease;
}

/* Auth Page Background with Animated Gradient */
.auth-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  font-family: var(--font-secondary);
  color: var(--dark);
}

.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-bg);
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
  z-index: -2;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  z-index: -1;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Navbar Styling */
.auth-navbar {
  padding: 1.25rem 0; /* Reduced padding */
  position: relative;
  z-index: 10;
}

.auth-navbar .navbar-brand {
  display: flex;
  align-items: center;
  color: var(--white);
  font-weight: 700;
  font-size: 1.3rem; /* Reduced size */
  text-decoration: none;
  transition: all var(--transition-fast);
}

.auth-navbar .navbar-brand i {
  color: var(--secondary);
  margin-right: 0.5rem;
  font-size: 1.5rem; /* Reduced size */
  transition: all var(--transition-fast);
  filter: drop-shadow(0 0 5px rgba(122, 226, 207, 0.5)); /* Added glow effect */
}

.auth-navbar .navbar-brand:hover {
  transform: translateY(-2px);
}

.auth-navbar .navbar-brand:hover i {
  transform: scale(1.1) rotate(5deg);
  color: var(--white); /* Changed hover color */
}

.btn-home {
  background: rgba(255, 255, 255, 0.15); /* Semi-transparent background */
  color: var(--white);
  border: 1px solid rgba(255, 255, 255, 0.3); /* Lighter border */
  border-radius: var(--border-radius-md);
  padding: 0.4rem 0.9rem; /* Reduced padding */
  font-weight: 600;
  font-size: 0.9rem; /* Reduced font size */
  transition: all var(--transition-fast);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  backdrop-filter: blur(5px); /* Added blur effect */
}

.btn-home:hover {
  background: var(--white);
  color: var(--primary);
  transform: translateY(-2px); /* Reduced lift */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1); /* Reduced shadow */
  border-color: var(--white);
}

.btn-home i {
  margin-right: 0.4rem; /* Reduced margin */
  font-size: 0.9rem; /* Reduced size */
}

/* Auth Container & Card */
.auth-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 1rem;
  position: relative;
  z-index: 5;
}

.auth-card {
  background: var(--gradient-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  width: 100%;
  max-width: 420px; /* Reduced width */
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: transform var(--transition-medium),
    box-shadow var(--transition-medium);
}

.auth-card:hover {
  transform: translateY(-8px); /* Reduced hover lift */
  box-shadow: var(--box-shadow-hover);
}

.auth-card::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(122, 226, 207, 0.1) 0%,
    transparent 70%
  );
  transform: rotate(45deg);
  z-index: 0;
}

.auth-card-header {
  position: relative;
  padding: 1.75rem 1.5rem 1rem; /* Reduced padding */
  z-index: 1;
}

.auth-card-body {
  position: relative;
  padding: 0 1.5rem 1.75rem; /* Reduced padding */
  z-index: 1;
}

/* Auth Logo Animation */
.auth-logo {
  width: 60px; /* Reduced size */
  height: 60px; /* Reduced size */
  margin: 0 auto 1rem; /* Reduced margin */
  background: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(7, 122, 125, 0.2); /* Reduced shadow */
  position: relative;
  overflow: hidden;
  transition: all var(--transition-medium);
}

.auth-logo::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(122, 226, 207, 0.3) 0%,
    transparent 70%
  );
  animation: rotateLogo 8s linear infinite;
}

@keyframes rotateLogo {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.auth-logo i {
  color: var(--primary);
  font-size: 2rem; /* Reduced size */
  position: relative;
  z-index: 1;
  transition: all var(--transition-medium);
}

.auth-card:hover .auth-logo {
  transform: scale(1.05);
}

.auth-card:hover .auth-logo i {
  transform: scale(1.1) rotate(10deg);
  color: var(--primary-dark);
}

/* Typography */
.auth-title {
  font-size: 1.8rem; /* Reduced size */
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 0.5rem; /* Reduced margin */
  position: relative;
  display: inline-block;
}

.auth-title::after {
  content: "";
  position: absolute;
  bottom: -6px; /* Reduced position */
  left: 50%;
  transform: translateX(-50%);
  width: 40px; /* Reduced width */
  height: 3px;
  background: var(--gradient-btn-primary);
  border-radius: 3px;
}

.auth-subtitle {
  color: var(--gray);
  font-size: 0.95rem; /* Reduced size */
  margin-bottom: 1rem; /* Reduced margin */
  line-height: 1.4; /* Reduced line height */
}

/* Features List */
.features-list {
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--border-radius-md);
  padding: 1rem; /* Reduced padding */
  border: 1px solid rgba(122, 226, 207, 0.3);
  margin-bottom: 1.25rem; /* Reduced margin */
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem; /* Reduced margin */
  transition: all var(--transition-fast);
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-item:hover {
  transform: translateX(5px);
}

.feature-item i {
  color: var(--success);
  font-size: 1rem; /* Reduced size */
  margin-right: 0.5rem; /* Reduced margin */
  transition: all var(--transition-fast);
}

.feature-item:hover i {
  transform: scale(1.2);
}

.feature-item span {
  font-weight: 500;
  color: var(--dark);
  font-size: 0.9rem; /* Reduced size */
}

/* Google Button */
.btn-google {
  background: var(--white);
  color: var(--dark);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-md);
  padding: 0.75rem; /* Reduced padding */
  font-weight: 600;
  font-size: 1rem; /* Reduced font size */
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* Reduced shadow */
  text-decoration: none;
}

.btn-google::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: all 0.6s ease;
  z-index: -1;
}

.btn-google:hover {
  transform: translateY(-4px); /* Reduced lift */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Reduced shadow */
  border-color: var(--secondary);
  color: var(--dark);
}

.btn-google:hover::before {
  left: 100%;
}

.btn-google:active {
  transform: translateY(-2px);
}

.google-icon {
  width: 20px; /* Reduced size */
  height: 20px; /* Reduced size */
  object-fit: contain;
  margin-right: 0.5rem; /* Reduced margin */
  transition: all var(--transition-fast);
}

.btn-google:hover .google-icon {
  transform: rotate(10deg);
}

/* Account Toggle Link */
.account-toggle {
  text-align: center;
  margin-top: 1rem; /* Reduced margin */
}

.account-toggle p {
  color: var(--gray);
  margin-bottom: 0;
  font-size: 0.9rem; /* Reduced size */
}

.account-toggle a {
  color: var(--primary);
  font-weight: 700;
  text-decoration: none;
  position: relative;
  transition: all var(--transition-fast);
}

.account-toggle a::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary);
  transition: all var(--transition-fast);
}

.account-toggle a:hover {
  color: var(--primary-dark);
}

.account-toggle a:hover::after {
  width: 100%;
}

/* Responsive Styles */
@media (max-width: 576px) {
  .auth-card {
    max-width: 100%;
  }

  .auth-title {
    font-size: 1.8rem;
  }

  .auth-subtitle {
    font-size: 1rem;
  }

  .auth-logo {
    width: 70px;
    height: 70px;
  }

  .auth-logo i {
    font-size: 2rem;
  }

  .auth-card-header {
    padding: 2rem 1.5rem 1rem;
  }

  .auth-card-body {
    padding: 0 1.5rem 2rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn var(--transition-medium) forwards;
}

.slide-up {
  animation: slideUp var(--transition-medium) forwards;
}

/* Animation Delays */
.delay-1 {
  animation-delay: 0.1s;
}

.delay-2 {
  animation-delay: 0.2s;
}

.delay-3 {
  animation-delay: 0.3s;
}

.delay-4 {
  animation-delay: 0.4s;
}

.delay-5 {
  animation-delay: 0.5s;
}
