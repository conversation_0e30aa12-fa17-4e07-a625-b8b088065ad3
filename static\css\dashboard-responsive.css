/* Dashboard Responsive Enhancements */

/* Mobile sidebar improvements */
@media (max-width: 992px) {
  .sidebar {
    width: 280px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1040; /* Higher than the default to ensure it appears above other content */
  }

  /* Add overlay when sidebar is open */
  .sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1030;
  }

  .sb-sidenav-toggled .sidebar-overlay {
    display: block;
  }

  /* Improve topbar on mobile */
  #topbar .navbar-brand {
    font-size: 1.2rem;
  }
}

/* Small mobile devices */
@media (max-width: 576px) {
  .content {
    padding: 1rem 0.75rem;
  }

  /* Card header improvements for mobile */
  .card-header {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start !important;
  }

  .card-header h5 {
    margin-bottom: 0.5rem;
  }

  .agent-status {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /* Improve button spacing on mobile */
  .btn {
    padding: 0.5rem 1rem;
  }

  /* Adjust table for mobile */
  .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Improve logs panel on mobile */
  .logs-panel {
    height: 250px;
  }

  /* Timeline improvements for mobile */
  .timeline {
    padding-left: 1.5rem;
  }

  .timeline-marker {
    left: -1.5rem;
    width: 10px;
    height: 10px;
  }
}

/* Medium devices */
@media (min-width: 577px) and (max-width: 991px) {
  .content {
    padding: 1.5rem;
  }
}

/* Responsive table improvements */
.table-responsive-card {
  border: 0;
}

@media (max-width: 767px) {
  .table-responsive-card thead {
    display: none;
  }

  .table-responsive-card tbody tr {
    display: block;
    margin-bottom: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .table-responsive-card tbody td {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: none;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .table-responsive-card tbody td:last-child {
    border-bottom: none;
  }

  .table-responsive-card tbody td:before {
    content: attr(data-label);
    font-weight: 600;
    margin-right: 1rem;
  }

  .table-responsive-card tbody td:last-child {
    text-align: right;
    justify-content: flex-end;
  }
}

/* Dashboard card improvements */
.dashboard-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

/* Stat cards improvements */
.stat-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1.25rem !important;
  border-radius: 0.75rem !important;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-top: auto;
}

/* Improved activity timeline */
.timeline-item {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 1.25rem;
}

.timeline-marker {
  position: absolute;
  left: 0;
  top: 0.25rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.timeline-content {
  background-color: #f8f9fa;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border-left: none;
}

/* Improved logs panel */
.logs-container {
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  overflow: hidden;
}

.logs-panel {
  border: none;
  max-height: 50vh;
  overflow-y: auto;
  scrollbar-width: thin;
}

.logs-panel::-webkit-scrollbar {
  width: 6px;
}

.logs-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.logs-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.logs-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Logs controls styling */
.logs-controls {
  padding: 0.5rem 0;
}

.logs-controls .btn-group .btn {
  border-radius: 0.375rem;
  margin: 0 1px;
}

.logs-controls .btn-group .btn:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.logs-controls .btn-group .btn:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.logs-status {
  padding: 0.5rem 0;
  font-size: 0.8rem;
}

.logs-status button {
  color: #6c757d;
}

.logs-status button:hover {
  color: #495057;
}

/* Improved buttons */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-btn i {
  margin-right: 0.5rem;
}

/* Utility classes */
.mb-sm-0 {
  margin-bottom: 0;
}

@media (max-width: 576px) {
  .mb-sm-3 {
    margin-bottom: 1rem !important;
  }
}

/* Improved modal for mobile */
@media (max-width: 576px) {
  .modal-dialog {
    margin: 0.5rem;
  }

  .modal-content {
    border-radius: 0.75rem;
  }

  /* Logs modal improvements for mobile */
  .logs-controls .btn-group {
    width: 100%;
    margin-top: 0.5rem;
  }

  .logs-controls .btn-group .btn {
    flex: 1;
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }

  .logs-controls #clear-logs-search {
    margin-top: 0.5rem;
    width: 100%;
  }

  .logs-status {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .logs-status > div:first-child {
    margin-bottom: 0.5rem;
  }

  /* Improve log entry display on mobile */
  .log-entry {
    padding: 0.75rem 0.5rem;
  }

  .modal .logs-panel {
    max-height: 40vh;
  }
}
