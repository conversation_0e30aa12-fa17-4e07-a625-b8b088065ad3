<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard - Email Reply Agent</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/styles.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard-enhanced.css') }}"
    />
  </head>
  <body class="dashboard-body">
    <!-- Toast Container for Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Agent Loading Overlay -->
    <div class="agent-loading-overlay" id="agentLoadingOverlay">
      <div class="agent-loading-container">
        <div class="agent-loading-spinner">
          <i class="bi bi-envelope-paper-heart agent-loading-icon"></i>
        </div>
        <div class="agent-loading-text">Starting Email Agent</div>
        <div class="agent-loading-subtext">
          Please wait while we initialize your email assistant...
        </div>
      </div>
    </div>

    <!-- Topbar -->
    <nav id="topbar">
      <button class="btn" id="sidebarToggle" type="button">
        <i class="bi bi-list"></i>
      </button>
      <a class="navbar-brand" href="{{ url_for('dashboard') }}">
        <i class="bi bi-envelope-paper-heart"></i>
        Email Reply Agent
      </a>
      <div class="topbar-actions">
        <div class="user-info">
          <div class="user-avatar">{{ current_user.name[0] }}</div>
          <div class="user-name">{{ current_user.name }}</div>
        </div>
        <a href="{{ url_for('logout') }}" class="btn-logout">
          <i class="bi bi-box-arrow-right"></i>Logout
        </a>
      </div>
    </nav>

    <!-- Sidebar Overlay (for mobile) -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <div class="sidebar">
      <div class="sidebar-content">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a href="{{ url_for('dashboard') }}" class="nav-link active">
              <i class="bi bi-speedometer2"></i>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('email_preview') }}" class="nav-link">
              <i class="bi bi-envelope"></i>
              Email Replies
              <span class="badge" id="pending-replies-count">0</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('profile') }}" class="nav-link">
              <i class="bi bi-person"></i>
              Profile
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('settings') }}" class="nav-link">
              <i class="bi bi-gear"></i>
              Settings
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Content -->
    <main class="content">
      <div class="container-fluid px-4">
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %} {% for category, message in messages %}
        <div
          class="alert alert-{{ category }} alert-dismissible fade show"
          role="alert"
        >
          {{ message }}
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="alert"
          ></button>
        </div>
        {% endfor %} {% endif %} {% endwith %}

        <div class="row">
          <div class="col-12">
            <div class="dashboard-card mb-4">
              <div class="card-header">
                <h5>Email Agent Control</h5>
                <div class="agent-status">
                  <span>Status:</span>
                  <span id="status-badge" class="badge">Checking...</span>
                  <small id="status-time">Last updated: --</small>
                </div>
              </div>
              <div class="card-body">
                <div class="row mb-4">
                  <div class="col-6">
                    <button id="startBtn" class="action-btn btn-success">
                      <i class="bi bi-play-circle"></i>
                      <span class="d-none d-sm-inline">Start Agent</span>
                      <span class="d-inline d-sm-none">Start</span>
                    </button>
                  </div>
                  <div class="col-6">
                    <button id="stopBtn" class="action-btn btn-danger">
                      <i class="bi bi-stop-circle"></i>
                      <span class="d-none d-sm-inline">Stop Agent</span>
                      <span class="d-inline d-sm-none">Stop</span>
                    </button>
                  </div>
                </div>
                <div class="logs-container mt-3">
                  <h6
                    class="mb-2 d-flex justify-content-between align-items-center"
                  >
                    <span><i class="bi bi-activity"></i>Live Activity</span>
                    <div>
                      <button
                        id="viewAllLogsBtn"
                        class="btn-outline-primary action-btn"
                        data-bs-toggle="collapse"
                        data-bs-target="#logsCollapsiblePanel"
                        aria-expanded="false"
                        aria-controls="logsCollapsiblePanel"
                      >
                        <i class="bi bi-list-ul"></i>View All
                      </button>
                      <span class="badge" id="log-count">0</span>
                    </div>
                  </h6>

                  <!-- Live feed style logs display -->
                  <div id="live-logs" class="logs-panel">
                    <div class="latest-log-wrapper">
                      <div id="latest-log" class="latest-log">
                        <div class="placeholder-text">
                          Waiting for activity...
                        </div>
                      </div>
                    </div>
                    <div id="recent-logs" class="recent-logs">
                      <!-- Recent logs will be displayed here -->
                    </div>
                  </div>

                  <!-- Collapsible logs panel -->
                  <div id="logsCollapsiblePanel" class="mt-4 collapse">
                    <div class="card">
                      <div
                        class="card-header bg-light d-flex justify-content-between align-items-center"
                      >
                        <h6 class="mb-0">
                          <i class="bi bi-list-ul me-2"></i>Activity Logs
                        </h6>
                        <div>
                          <button
                            id="collapseLogsBtn"
                            class="btn btn-sm btn-outline-secondary"
                          >
                            <i class="bi bi-chevron-up"></i>
                          </button>
                        </div>
                      </div>
                      <div class="card-body p-3">
                        <!-- Logs filter controls -->
                        <div class="logs-controls mb-3">
                          <div class="row align-items-center">
                            <div class="col-md-6 mb-2 mb-md-0">
                              <div class="input-group">
                                <span class="input-group-text bg-light">
                                  <i class="bi bi-search"></i>
                                </span>
                                <input
                                  type="text"
                                  class="form-control"
                                  id="logs-search"
                                  placeholder="Search logs..."
                                />
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="d-flex gap-2 justify-content-md-end">
                                <div class="btn-group" role="group">
                                  <button
                                    type="button"
                                    class="btn btn-sm btn-outline-primary active"
                                    data-log-filter="all"
                                  >
                                    All
                                  </button>
                                  <button
                                    type="button"
                                    class="btn btn-sm btn-outline-success"
                                    data-log-filter="success"
                                  >
                                    Success
                                  </button>
                                  <button
                                    type="button"
                                    class="btn btn-sm btn-outline-danger"
                                    data-log-filter="error"
                                  >
                                    Errors
                                  </button>
                                  <button
                                    type="button"
                                    class="btn btn-sm btn-outline-info"
                                    data-log-filter="agent"
                                  >
                                    Agent
                                  </button>
                                </div>
                                <button
                                  id="clear-logs-search"
                                  class="btn btn-sm btn-outline-secondary"
                                >
                                  <i class="bi bi-x-circle"></i>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Logs container with improved styling -->
                        <div class="logs-container">
                          <div class="logs-header d-none d-md-flex">
                            <div class="log-col-time">Time</div>
                            <div class="log-col-type">Type</div>
                            <div class="log-col-message">Message</div>
                          </div>
                          <div id="all-logs" class="logs-panel"></div>
                        </div>

                        <!-- Logs status footer -->
                        <div
                          class="logs-status mt-3 d-flex justify-content-between align-items-center text-muted small"
                        >
                          <div>
                            <span id="logs-count-display">0</span> logs
                            displayed
                          </div>
                          <div>
                            <button
                              id="auto-scroll-toggle"
                              class="btn btn-sm btn-link text-decoration-none p-0"
                            >
                              <i class="bi bi-arrow-down-circle"></i>
                              Auto-scroll is ON
                            </button>
                          </div>
                        </div>
                      </div>
                      <div class="card-footer bg-light">
                        <div class="d-flex justify-content-between">
                          <button
                            type="button"
                            class="btn btn-sm btn-outline-secondary"
                            id="clear-filters-btn"
                          >
                            <i class="bi bi-arrow-counterclockwise me-1"></i>
                            Reset Filters
                          </button>
                          <button
                            type="button"
                            class="btn btn-sm btn-primary"
                            id="refreshLogsBtn"
                          >
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            Refresh
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6 mb-4">
            <div class="dashboard-card h-100">
              <div class="card-header">
                <h5>Quick Stats</h5>
              </div>
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-6">
                    <div class="stat-card">
                      <div class="d-flex align-items-center">
                        <i class="bi bi-envelope-check"></i>
                        <h6>Emails Processed</h6>
                      </div>
                      <div class="stat-value" id="emails-processed">0</div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="stat-card">
                      <div class="d-flex align-items-center">
                        <i class="bi bi-reply-all"></i>
                        <h6>Replies Sent</h6>
                      </div>
                      <div class="stat-value" id="replies-sent">0</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-4">
            <div class="dashboard-card h-100">
              <div class="card-header">
                <h5>Recent Activity</h5>
                <span class="badge" id="activity-count">0</span>
              </div>
              <div class="card-body">
                <div class="timeline" id="activity-timeline">
                  <!-- Timeline items will be added dynamically -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-12">
            <div class="dashboard-card mb-4">
              <div class="card-header">
                <h5>Automated Email Addresses</h5>
                <div>
                  <button
                    class="action-btn btn-primary"
                    data-bs-toggle="modal"
                    data-bs-target="#addEmailModal"
                  >
                    <i class="bi bi-plus-circle"></i>
                    <span class="d-none d-sm-inline">Add Email</span>
                    <span class="d-inline d-sm-none">Add</span>
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div
                  class="alert alert-info alert-dismissible fade show mb-3"
                  role="alert"
                >
                  <i class="bi bi-info-circle"></i>
                  <span
                    >The agent will only process emails from these addresses. If
                    no addresses are added, all unread emails will be
                    processed.</span
                  >
                  <button
                    type="button"
                    class="btn-close"
                    data-bs-dismiss="alert"
                  ></button>
                </div>
                <div id="automated-emails-container" class="table-responsive">
                  <div class="d-flex justify-content-center py-4">
                    <div class="spinner-border" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Add Email Modal -->
        <div
          class="modal fade"
          id="addEmailModal"
          tabindex="-1"
          aria-labelledby="addEmailModalLabel"
          aria-hidden="true"
        >
          <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content enhanced-modal">
              <div class="modal-header enhanced-modal-header">
                <div class="modal-title-container">
                  <h4 class="modal-title" id="addEmailModalLabel">
                    <div class="modal-icon">
                      <i class="bi bi-envelope-plus"></i>
                    </div>
                    <div class="modal-title-text">
                      <span class="title-main">Add Automated Email</span>
                      <span class="title-sub"
                        >Configure email automation settings</span
                      >
                    </div>
                  </h4>
                </div>
                <button
                  type="button"
                  class="btn-close enhanced-btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
              <div class="modal-body enhanced-modal-body">
                <form id="addEmailForm" class="enhanced-form">
                  <div class="form-section">
                    <div class="form-group-enhanced">
                      <label for="emailAddress" class="form-label-enhanced">
                        <i class="bi bi-envelope me-2"></i>
                        Email Address
                      </label>
                      <div class="input-container-enhanced">
                        <div class="input-group-enhanced">
                          <span class="input-icon">
                            <i class="bi bi-at"></i>
                          </span>
                          <input
                            type="email"
                            class="form-control-enhanced"
                            id="emailAddress"
                            placeholder="Enter email address (e.g., <EMAIL>)"
                            required
                            autocomplete="email"
                          />
                        </div>
                      </div>
                      <div class="form-help-text">
                        <div class="help-item">
                          <i class="bi bi-info-circle me-2"></i>
                          <span
                            >The agent will only process emails from this
                            address when automation is enabled.</span
                          >
                        </div>
                        <div class="help-item">
                          <i class="bi bi-shield-check me-2"></i>
                          <span
                            >Email addresses are validated and stored
                            securely.</span
                          >
                        </div>
                      </div>
                    </div>

                    <div class="form-group-enhanced">
                      <div class="feature-highlight">
                        <div class="feature-icon">
                          <i class="bi bi-lightning-charge"></i>
                        </div>
                        <div class="feature-content">
                          <h6>Smart Email Processing</h6>
                          <p>
                            Our AI agent will automatically generate contextual
                            replies for emails from this address, saving you
                            time and ensuring consistent communication.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    id="emailFormError"
                    class="alert alert-danger enhanced-alert d-none"
                    role="alert"
                  >
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="errorMessage"></span>
                  </div>

                  <div
                    id="emailFormSuccess"
                    class="alert alert-success enhanced-alert d-none"
                    role="alert"
                  >
                    <i class="bi bi-check-circle me-2"></i>
                    <span>Email address validation successful!</span>
                  </div>
                </form>
              </div>
              <div class="modal-footer enhanced-modal-footer">
                <button
                  type="button"
                  class="btn btn-outline-secondary enhanced-btn-secondary"
                  data-bs-dismiss="modal"
                >
                  <i class="bi bi-x-circle me-2"></i>
                  Cancel
                </button>
                <button
                  type="button"
                  class="btn btn-primary enhanced-btn-primary"
                  id="saveEmailBtn"
                >
                  <i class="bi bi-plus-circle me-2"></i>
                  <span class="btn-text">Add Email Address</span>
                  <span class="btn-spinner d-none">
                    <span
                      class="spinner-border spinner-border-sm me-2"
                      role="status"
                    ></span>
                    Adding...
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/unified-dashboard.js') }}"></script>
    <script>
      // Agent controls
      const startBtn = document.getElementById("startBtn");
      const stopBtn = document.getElementById("stopBtn");
      const statusBadge = document.getElementById("status-badge");

      // Variables for debouncing status changes
      let lastStatusValue = null;
      let statusChangeCount = 0;
      let statusDebounceTimer = null;
      const STATUS_DEBOUNCE_DELAY = 2000; // 2 seconds debounce delay
      // Add new variables for more robust state handling
      let stableStatusValue = null;
      let stableStatusTimestamp = 0;
      const STABILITY_THRESHOLD = 5000; // 5 seconds of stability required before accepting state change
      let lastStableState = null; // Track the last known stable state

      function updateStatus() {
        fetch("/status")
          .then((response) => {
            if (!response.ok) {
              throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
          })
          .then((data) => {
            console.log("Status response:", data);

            // Determine the actual status based on the running flag
            // This now considers both the global agent and the user's agent
            const isRunning = data.running;

            // Get more detailed status if available
            const details = data.details || {};
            const userAgentRunning = details.user_agent || false;
            const globalAgentRunning = details.global_agent || false;
            const recentlyStopped = details.recently_stopped || false;
            const threadAlive = data.thread_alive || false;
            const isProduction = details.production_mode || false;

            // Current time for stability calculations
            const now = Date.now();

            // Determine status text and class
            let statusText = "Stopped";
            let statusClass = "bg-danger";

            if (isRunning) {
              statusText = "Running";
              statusClass = "bg-success";
            } else if (userAgentRunning) {
              // If user agent is running but global status says not running
              statusText = "Starting...";
              statusClass = "bg-info";
            } else if (recentlyStopped) {
              // If agent was recently stopped
              statusText = "Stopped";
              statusClass = "bg-danger";
            } else if (!isRunning && threadAlive) {
              // If running flag is false but thread is alive
              statusText = "Stopping...";
              statusClass = "bg-warning";
            } else if (isRunning && !threadAlive) {
              // If running flag is true but thread is not alive
              statusText = "Error";
              statusClass = "bg-warning";
            }

            // Enhanced state management logic
            if (lastStatusValue === null) {
              // First status update - set initial values
              lastStatusValue = isRunning;
              stableStatusValue = isRunning;
              stableStatusTimestamp = now;
              lastStableState = isRunning;

              // Update UI immediately for first load
              updateStatusUI(
                statusText,
                statusClass,
                isRunning,
                userAgentRunning,
                globalAgentRunning,
                data.timestamp
              );
            } else if (isRunning !== lastStatusValue) {
              // Status has changed
              statusChangeCount++;
              console.log(
                `Status changed from ${lastStatusValue} to ${isRunning} (change #${statusChangeCount})`
              );
              lastStatusValue = isRunning;

              // If we've seen too many changes, don't update UI until it stabilizes
              if (statusChangeCount > 2) {
                console.log(
                  `Status fluctuating (${statusChangeCount} changes), waiting for stability`
                );

                // Reset stability timer since status changed
                stableStatusValue = isRunning;
                stableStatusTimestamp = now;

                // Use the last known stable state for UI
                if (lastStableState !== null) {
                  const stableText = lastStableState ? "Running" : "Stopped";
                  const stableClass = lastStableState
                    ? "bg-success"
                    : "bg-danger";

                  console.log(
                    `Using last stable state: ${stableText} while waiting for stability`
                  );

                  updateStatusUI(
                    stableText,
                    stableClass,
                    lastStableState,
                    userAgentRunning,
                    globalAgentRunning,
                    data.timestamp + " (stabilizing)"
                  );
                }
              } else {
                // For the first couple changes, update normally
                updateStatusUI(
                  statusText,
                  statusClass,
                  isRunning,
                  userAgentRunning,
                  globalAgentRunning,
                  data.timestamp
                );

                // Reset stability timer
                stableStatusValue = isRunning;
                stableStatusTimestamp = now;
              }
            } else {
              // Status is the same as last check
              // If it's been stable for STABILITY_THRESHOLD ms, accept it as the new stable state
              if (
                isRunning === stableStatusValue &&
                now - stableStatusTimestamp >= STABILITY_THRESHOLD
              ) {
                if (lastStableState !== isRunning) {
                  console.log(
                    `Status has stabilized at ${
                      isRunning ? "Running" : "Stopped"
                    } for ${STABILITY_THRESHOLD}ms, accepting as stable state`
                  );
                  lastStableState = isRunning;
                  statusChangeCount = 0; // Reset change counter as we've reached stability
                }

                // Update UI with stable state
                updateStatusUI(
                  statusText,
                  statusClass,
                  isRunning,
                  userAgentRunning,
                  globalAgentRunning,
                  data.timestamp
                );
              } else if (isRunning !== stableStatusValue) {
                // Status is consistent with last check but different from what we're waiting to stabilize
                // Update the stable value we're tracking
                stableStatusValue = isRunning;
                stableStatusTimestamp = now;

                // Still use the last stable state for UI
                if (lastStableState !== null && statusChangeCount > 2) {
                  const stableText = lastStableState ? "Running" : "Stopped";
                  const stableClass = lastStableState
                    ? "bg-success"
                    : "bg-danger";

                  updateStatusUI(
                    stableText,
                    stableClass,
                    lastStableState,
                    userAgentRunning,
                    globalAgentRunning,
                    data.timestamp + " (stabilizing)"
                  );
                } else {
                  updateStatusUI(
                    statusText,
                    statusClass,
                    isRunning,
                    userAgentRunning,
                    globalAgentRunning,
                    data.timestamp
                  );
                }
              } else {
                // Status is consistent and we're still waiting for stability timer
                // Keep updating UI with current state if not fluctuating too much
                if (statusChangeCount <= 2) {
                  updateStatusUI(
                    statusText,
                    statusClass,
                    isRunning,
                    userAgentRunning,
                    globalAgentRunning,
                    data.timestamp
                  );
                }
              }
            }

            // Only hide the loading overlay when the agent has reached a stable state
            // and that state matches what we expect
            if (window.expectedAgentState && lastStableState !== null) {
              const stateMatches =
                (window.expectedAgentState === "running" &&
                  lastStableState === true) ||
                (window.expectedAgentState === "stopped" &&
                  lastStableState === false);

              if (stateMatches || statusText === "Error") {
                console.log(
                  `Agent reached stable expected state: ${statusText}, hiding loading overlay`
                );
                // Try to hide the loading overlay using the function from unified-dashboard.js
                if (typeof hideAgentLoadingOverlay === "function") {
                  hideAgentLoadingOverlay();
                  // Reset the expected state after it's been achieved
                  window.expectedAgentState = null;
                } else {
                  // Fallback: Hide the overlay directly if the function is not available
                  console.log(
                    "hideAgentLoadingOverlay function not found, using fallback"
                  );
                  const agentLoadingOverlay = document.getElementById(
                    "agentLoadingOverlay"
                  );
                  if (agentLoadingOverlay) {
                    agentLoadingOverlay.classList.remove("show");
                    // Reset the expected state after it's been achieved
                    window.expectedAgentState = null;
                  }
                }
              } else if (window.expectedAgentState) {
                console.log(
                  `Agent transitioning to ${window.expectedAgentState}, current status: ${statusText}. Keeping overlay visible.`
                );
              }
            }
          })
          .catch((error) => {
            console.error("Error fetching status:", error);
            updateStatusUI("Error", "bg-warning", false, false, false, "Error");
          });
      }

      // Function to update the status UI elements
      function updateStatusUI(
        statusText,
        statusClass,
        isRunning,
        userAgentRunning,
        globalAgentRunning,
        timestamp
      ) {
        // Update the status badge
        statusBadge.textContent = statusText;
        statusBadge.className = `badge rounded-pill ${statusClass}`;

        // Update button states
        startBtn.disabled = isRunning || userAgentRunning;
        stopBtn.disabled = !isRunning && !userAgentRunning;

        // Add timestamp to status for debugging
        const statusTime = document.getElementById("status-time");
        if (statusTime) {
          statusTime.textContent = timestamp || "Unknown";
        }

        // Add a tooltip with detailed status information
        statusBadge.title = `Global Agent: ${
          globalAgentRunning ? "Running" : "Stopped"
        }, User Agent: ${userAgentRunning ? "Running" : "Stopped"}`;
      }

      function updateStats() {
        // Get stats directly from the logs endpoint
        fetch("/logs")
          .then((response) => response.json())
          .then((data) => {
            if (data && data.stats) {
              // Update the stats in the UI directly from the API
              document.getElementById("emails-processed").textContent =
                data.stats.emails_processed;
              document.getElementById("replies-sent").textContent =
                data.stats.replies_sent;

              // Update the activity timeline
              if (data.logs && Array.isArray(data.logs)) {
                updateActivityTimeline(data.logs);
              }
            }
          })
          .catch((error) => {
            console.error("Error fetching stats:", error);
          });
      }

      function updateActivityTimeline(logs) {
        const timeline = document.getElementById("activity-timeline");
        timeline.innerHTML = ""; // Clear existing timeline

        // Get the last 5 significant activities from logs
        const significantLogs = logs
          .filter(
            (log) =>
              log.includes("Agent started") ||
              log.includes("Agent stopped") ||
              log.includes("Found") ||
              log.includes("Processing:") ||
              log.includes("Reply sent to") ||
              log.includes("Reply generated")
          )
          .slice(-5);

        // Update activity count badge
        const activityCount = document.getElementById("activity-count");
        if (activityCount) {
          activityCount.textContent = significantLogs.length;
        }

        // Add each activity to the timeline
        significantLogs.forEach((log) => {
          const timelineItem = document.createElement("div");
          timelineItem.className = "timeline-item mb-3"; // Add margin for better spacing

          // Determine the color based on the log content
          let color = "bg-primary";
          if (log.includes("Agent started")) color = "bg-success";
          if (log.includes("Agent stopped")) color = "bg-danger";
          if (log.includes("Reply sent")) color = "bg-info";
          if (log.includes("Reply generated")) color = "bg-warning";
          if (log.includes("Found") && log.includes("unread emails"))
            color = "bg-info";

          // Extract timestamp if available
          const timestamp = log.match(/\[(.*?)\]/)
            ? log.match(/\[(.*?)\]/)[1]
            : "Just now";
          let message = log.replace(/\[.*?\]\s*/, "");

          // Truncate very long messages
          if (message.length > 100) {
            message = message.substring(0, 100) + "...";
          }

          timelineItem.innerHTML = `
                    <div class="timeline-marker ${color}"></div>
                    <div class="timeline-content">
                        <p class="mb-0">${message}</p>
                        <small class="text-muted">${timestamp}</small>
                    </div>
                `;

          timeline.appendChild(timelineItem);
        });

        // If no activities, show a placeholder
        if (significantLogs.length === 0) {
          const timelineItem = document.createElement("div");
          timelineItem.className = "timeline-item";
          timelineItem.innerHTML = `
                    <div class="timeline-marker bg-secondary"></div>
                    <div class="timeline-content">
                        <p class="mb-0">No recent activity</p>
                        <small class="text-muted">Start the agent to begin monitoring</small>
                    </div>
                `;
          timeline.appendChild(timelineItem);
        }
      }

      // Variables to track logs state
      let lastLogMessage = "";
      let recentLogsCount = 3; // Number of recent logs to show
      let lastLogsData = []; // Store the last logs data
      let recentNotifications = {}; // Track recent notifications to prevent duplicates

      document
        .getElementById("viewAllLogsBtn")
        .addEventListener("click", function () {
          // Update logs when the button is clicked
          // The collapsible panel will be shown by Bootstrap's data attributes
          updateLogsPanel(lastLogsData || []);
        });

      function updateLogs() {
        fetch("/logs")
          .then((response) => response.json())
          .then((data) => {
            if (data && data.logs && Array.isArray(data.logs)) {
              // Store the logs data for later use
              lastLogsData = data.logs;

              // Update log count badge
              const logCount = document.getElementById("log-count");
              if (logCount) {
                logCount.textContent = data.logs.length;
              }

              // Get the first log message to check if it's new
              const firstLogMessage = data.logs.length > 0 ? data.logs[0] : "";
              const isNewLog = firstLogMessage !== lastLogMessage;

              // Update the live logs display (latest + recent)
              if (data.logs.length > 0) {
                // Update the last log message
                lastLogMessage = data.logs[0];

                // Update the live logs display
                updateLiveLogs(data.logs, isNewLog);

                // Update the collapsible logs panel if it's visible
                if (
                  logsVisible ||
                  (logsCollapsiblePanel &&
                    logsCollapsiblePanel.classList.contains("show"))
                ) {
                  updateLogsPanel(data.logs);
                }

                // Update stats and timeline when logs are updated
                updateStats();
              }
            } else {
              console.warn("Logs data is not in expected format:", data);
            }
          })
          .catch((error) => {
            console.error("Error fetching logs:", error);
          });
      }

      // Function to update the logs panel (all logs)
      function updateLogsPanel(logs) {
        // Only update if the logs panel is visible
        if (!logsVisible && !logsCollapsiblePanel.classList.contains("show"))
          return;

        const logsCountDisplay = document.getElementById("logs-count-display");
        if (!logsPanel) return;

        logsPanel.innerHTML = "";

        // If no logs, show a message
        if (!logs || logs.length === 0) {
          const emptyMessage = document.createElement("div");
          emptyMessage.className = "text-center text-muted p-4";
          emptyMessage.innerHTML =
            "<i class='bi bi-info-circle me-2'></i>No logs available yet.";
          logsPanel.appendChild(emptyMessage);
          if (logsCountDisplay) logsCountDisplay.textContent = "0";
          return;
        }

        // Filter out logs that just say "Logs requested"
        const filteredLogs = logs.filter(
          (log) => !log.includes("Logs requested")
        );

        // Update logs count display
        if (logsCountDisplay)
          logsCountDisplay.textContent = filteredLogs.length.toString();

        // Add each log entry to the panel
        filteredLogs.forEach((log, index) => {
          const logEntry = document.createElement("div");
          logEntry.className = "log-entry";

          // Add data attributes for filtering
          if (
            log.includes("Reply sent") ||
            log.includes("generated successfully")
          ) {
            logEntry.dataset.logType = "success";
          } else if (
            log.includes("Error") ||
            log.includes("error") ||
            log.includes("failed")
          ) {
            logEntry.dataset.logType = "error";
          } else if (
            log.includes("Agent started") ||
            log.includes("Agent stopped")
          ) {
            logEntry.dataset.logType = "agent";
          } else {
            logEntry.dataset.logType = "info";
          }

          // Extract timestamp and message
          const timestampMatch = log.match(/\[(.*?)\]/);
          const timestamp = timestampMatch ? timestampMatch[1] : "";
          const message = log.replace(/\[.*?\]\s*/, "");

          // Determine log type for badge
          let badgeClass = "bg-secondary";
          let badgeText = "Info";
          let badgeIcon = "info-circle";

          if (
            log.includes("Reply sent") ||
            log.includes("generated successfully")
          ) {
            badgeClass = "bg-success";
            badgeText = "Success";
            badgeIcon = "check-circle";
          } else if (
            log.includes("Error") ||
            log.includes("error") ||
            log.includes("failed")
          ) {
            badgeClass = "bg-danger";
            badgeText = "Error";
            badgeIcon = "exclamation-circle";
          } else if (log.includes("Agent started")) {
            badgeClass = "bg-success";
            badgeText = "Started";
            badgeIcon = "play-circle";
          } else if (log.includes("Agent stopped")) {
            badgeClass = "bg-danger";
            badgeText = "Stopped";
            badgeIcon = "stop-circle";
          } else if (log.includes("Found") && log.includes("unread emails")) {
            badgeClass = "bg-info";
            badgeText = "Emails";
            badgeIcon = "envelope";
          } else if (log.includes("Processing")) {
            badgeClass = "bg-primary";
            badgeText = "Process";
            badgeIcon = "arrow-repeat";
          }

          // Create a structured log entry with columns
          logEntry.innerHTML = `
            <div class="log-timestamp">${timestamp}</div>
            <div class="log-type">
              <span class="badge ${badgeClass}">
                <i class="bi bi-${badgeIcon} me-1"></i>${badgeText}
              </span>
            </div>
            <div class="log-message">${message}</div>
          `;

          logsPanel.appendChild(logEntry);
        });

        // Scroll to bottom if auto-scroll is enabled
        if (autoScrollEnabled) {
          logsPanel.scrollTop = logsPanel.scrollHeight;
        }

        // Apply any active filters
        applyLogFilters();
      }

      // Function to update the live logs display (latest + recent)
      function updateLiveLogs(logs, isNewLog) {
        // Filter out "Logs requested" entries
        logs = logs.filter((log) => !log.includes("Logs requested"));
        if (logs.length === 0) return;

        // Update latest log
        const latestLog = logs[0];
        const latestLogElement = document.getElementById("latest-log");

        // Extract timestamp and message
        const timestampMatch = latestLog.match(/\[(.*?)\]/);
        const timestamp = timestampMatch ? timestampMatch[1] : "";
        const message = latestLog.replace(/\[.*?\]\s*/, "");

        // Determine log type for styling
        let logType = "success";
        if (latestLog.includes("Error") || latestLog.includes("error")) {
          logType = "error";
        } else if (
          latestLog.includes("Warning") ||
          latestLog.includes("warning")
        ) {
          logType = "warning";
        } else if (latestLog.includes("Agent started")) {
          logType = "success";
        } else if (latestLog.includes("Agent stopped")) {
          logType = "error";
        } else if (latestLog.includes("Reply sent")) {
          logType = "success";
        } else if (
          latestLog.includes("Found") &&
          latestLog.includes("unread emails")
        ) {
          logType = "info";
        }

        // Update the latest log content
        latestLogElement.innerHTML = `
          <span class="log-timestamp">${timestamp}</span>
          <span class="log-message">${message}</span>
        `;

        // Update classes for styling
        latestLogElement.className = "latest-log";
        latestLogElement.classList.add(logType);

        // Add animation class if it's a new log
        if (isNewLog) {
          latestLogElement.classList.add("new-entry");
          setTimeout(() => {
            latestLogElement.classList.remove("new-entry");
          }, 1000);

          // Show toast notification for important events
          if (message) {
            // Only show notifications for important events
            if (
              message.includes("Agent started") &&
              !message.includes("requested")
            ) {
              showToast(
                "Agent Status Update",
                "Agent has been started successfully",
                "success"
              );
            } else if (
              message.includes("Agent stopped") &&
              !message.includes("requested")
            ) {
              showToast(
                "Agent Status Update",
                "Agent has been stopped successfully",
                "danger"
              );
            } else if (message.includes("Reply sent")) {
              showToast(
                "Email Sent",
                "A reply has been sent successfully",
                "success"
              );
            } else if (
              (message.includes("Error") || message.includes("error")) &&
              !message.includes("start") &&
              !message.includes("stop")
            ) {
              // Only show error messages that aren't related to agent start/stop
              // since those are handled by the button click handlers
              showToast("System Error", message, "danger");
            } else if (
              message.includes("Found") &&
              message.includes("unread emails")
            ) {
              const count = message.match(/Found (\d+) unread emails/);
              if (count && count[1] && parseInt(count[1]) > 0) {
                showToast(
                  "New Emails",
                  `Found ${count[1]} unread emails`,
                  "info"
                );
              }
            }
          }
        }

        // Update recent logs (skip the first one as it's already shown as latest)
        const recentLogsElement = document.getElementById("recent-logs");
        recentLogsElement.innerHTML = "";

        // Show only a limited number of recent logs
        const recentLogsToShow = logs.slice(1, recentLogsCount + 1);

        recentLogsToShow.forEach((log) => {
          // Extract timestamp and message
          const timestampMatch = log.match(/\[(.*?)\]/);
          const timestamp = timestampMatch ? timestampMatch[1] : "";
          const message = log.replace(/\[.*?\]\s*/, "");

          // Determine log type for styling
          let logClass = "";
          if (log.includes("Error") || log.includes("error")) {
            logClass = "error";
          } else if (log.includes("Warning") || log.includes("warning")) {
            logClass = "warning";
          } else if (
            log.includes("Agent started") ||
            log.includes("Reply sent")
          ) {
            logClass = "success";
          } else if (log.includes("Found") && log.includes("unread emails")) {
            logClass = "info";
          }

          // Create the recent log element
          const recentLogElement = document.createElement("div");
          recentLogElement.className = `recent-log ${logClass}`;
          recentLogElement.innerHTML = `
            <div class="log-content">${message}</div>
            <div class="log-time">${timestamp}</div>
          `;

          recentLogsElement.appendChild(recentLogElement);
        });
      }

      // Function to show toast notifications with deduplication
      function showToast(title, message, type = "info") {
        console.log("Showing toast:", title, message, type);

        // Create a unique key for this notification to prevent duplicates
        const notificationKey = `${title}-${message}-${type}`;

        // Check if we've shown this exact notification in the last 2 seconds
        // Reduced from 5 seconds to allow more notifications
        const now = Date.now();
        if (
          recentNotifications[notificationKey] &&
          now - recentNotifications[notificationKey] < 2000
        ) {
          console.log("Preventing duplicate notification:", notificationKey);
          return; // Skip showing duplicate notification
        }

        // Record this notification
        recentNotifications[notificationKey] = now;

        // Clean up old notifications from tracking object
        Object.keys(recentNotifications).forEach((key) => {
          if (now - recentNotifications[key] > 10000) {
            // Remove after 10 seconds
            delete recentNotifications[key];
          }
        });

        const toastId = "toast-" + Date.now();
        const toastHtml = `
          <div class="toast toast-${type}" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
              <i class="bi ${
                type === "success"
                  ? "bi-check-circle"
                  : type === "danger"
                  ? "bi-exclamation-circle"
                  : type === "warning"
                  ? "bi-exclamation-triangle"
                  : "bi-info-circle"
              } me-2"></i>
              <strong class="me-auto">${title}</strong>
              <small>${new Date().toLocaleTimeString()}</small>
              <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
              ${message}
            </div>
          </div>
        `;

        // Get toast container
        const toastContainer = document.getElementById("toastContainer");
        if (!toastContainer) {
          console.error("Toast container not found!");
          return;
        }

        // Add toast to container
        toastContainer.insertAdjacentHTML("beforeend", toastHtml);

        // Initialize and show the toast
        const toastElement = document.getElementById(toastId);
        if (!toastElement) {
          console.error("Toast element not found after insertion!");
          return;
        }

        try {
          // Make sure Bootstrap is available
          if (typeof bootstrap === "undefined") {
            console.error("Bootstrap is not defined!");
            // Fallback to manual display
            toastElement.classList.add("show");
            setTimeout(() => {
              toastElement.remove();
            }, 5000);
            return;
          }

          const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 5000,
          });
          toast.show();

          // Add animation class after a small delay to trigger the animation
          setTimeout(() => {
            toastElement.classList.add("show");
          }, 50);

          // Remove toast from DOM after it's hidden
          toastElement.addEventListener("hidden.bs.toast", () => {
            toastElement.remove();
          });
        } catch (error) {
          console.error("Error showing toast:", error);
          // Fallback to manual display
          toastElement.classList.add("show");
          setTimeout(() => {
            toastElement.remove();
          }, 5000);
        }
      }

      startBtn.addEventListener("click", () => {
        // Immediately disable both buttons to prevent multiple clicks
        startBtn.disabled = true;
        stopBtn.disabled = true;

        // Add visual indication that the button was clicked
        startBtn.classList.add("clicked");
        startBtn.innerHTML = `<i class="bi bi-hourglass-split"></i> <span class="d-none d-sm-inline">Starting...</span><span class="d-inline d-sm-none">Starting</span>`;

        // Set the expected state so the loading overlay knows what to wait for
        window.expectedAgentState = "running";

        // Show the loading overlay
        if (typeof showAgentLoadingOverlay === "function") {
          showAgentLoadingOverlay("start");
        } else {
          // Fallback: Show the overlay directly if the function is not available
          console.log(
            "showAgentLoadingOverlay function not found, using fallback"
          );
          const agentLoadingOverlay = document.getElementById(
            "agentLoadingOverlay"
          );
          if (agentLoadingOverlay) {
            agentLoadingOverlay.classList.add("show");
          }
        }

        fetch("/start", { method: "POST" })
          .then((response) => response.json())
          .then((data) => {
            console.log("Start agent response:", data);

            // Remove the clicked class and restore original button text
            startBtn.classList.remove("clicked");
            startBtn.innerHTML = `<i class="bi bi-play-circle"></i> <span class="d-none d-sm-inline">Start Agent</span><span class="d-inline d-sm-none">Start</span>`;

            // Update status immediately to reflect the change
            updateStatus();

            // Always show a notification for the action
            if (
              data.status.includes("Failed") ||
              data.status.includes("Error")
            ) {
              showToast("Agent Start Failed", data.status, "danger");

              // Hide loading overlay on error and re-enable buttons
              if (typeof hideAgentLoadingOverlay === "function") {
                hideAgentLoadingOverlay();
              } else {
                // Fallback: Hide the overlay directly if the function is not available
                console.log(
                  "hideAgentLoadingOverlay function not found, using fallback"
                );
                const agentLoadingOverlay = document.getElementById(
                  "agentLoadingOverlay"
                );
                if (agentLoadingOverlay) {
                  agentLoadingOverlay.classList.remove("show");
                }
              }

              // Re-enable buttons immediately on error
              startBtn.disabled = false;
              stopBtn.disabled = true;

              // Reset the expected state since we failed
              window.expectedAgentState = null;
            } else {
              showToast(
                "Agent Starting",
                "Agent start requested successfully",
                "success"
              );

              // Keep start button disabled until agent is confirmed running
              // The stop button will be enabled once the agent is confirmed running
              startBtn.disabled = true;
              stopBtn.disabled = false;

              // Keep the loading overlay visible until status changes to running
              // The overlay will be hidden by the updateStatus function once agent is running

              // Check status again after a short delay to ensure the overlay is hidden
              // This helps in production where the first status check might not catch the running state
              setTimeout(() => {
                console.log(
                  "Performing additional status check after agent start"
                );
                updateStatus();
              }, 3000);
            }

            // Update logs immediately to show the latest status
            updateLogs();
          })
          .catch((error) => {
            console.error("Error starting agent:", error);

            // Remove the clicked class and restore original button text
            startBtn.classList.remove("clicked");
            startBtn.innerHTML = `<i class="bi bi-play-circle"></i> <span class="d-none d-sm-inline">Start Agent</span><span class="d-inline d-sm-none">Start</span>`;

            // Re-enable the start button and disable stop button on error
            startBtn.disabled = false;
            stopBtn.disabled = true;

            // Reset the expected state since we failed
            window.expectedAgentState = null;

            // Hide loading overlay on error
            if (typeof hideAgentLoadingOverlay === "function") {
              hideAgentLoadingOverlay();
            } else {
              // Fallback: Hide the overlay directly if the function is not available
              console.log(
                "hideAgentLoadingOverlay function not found, using fallback"
              );
              const agentLoadingOverlay = document.getElementById(
                "agentLoadingOverlay"
              );
              if (agentLoadingOverlay) {
                agentLoadingOverlay.classList.remove("show");
              }
            }

            showToast(
              "Error",
              "Failed to start agent: " + (error.message || "Unknown error"),
              "danger"
            );
          });
      });

      stopBtn.addEventListener("click", () => {
        stopBtn.disabled = true; // Prevent multiple clicks
        startBtn.disabled = true; // Disable start button during stop operation

        // Set the expected state so the loading overlay knows what to wait for
        window.expectedAgentState = "stopped";

        // Show a loading overlay with stopping message
        if (typeof showAgentLoadingOverlay === "function") {
          showAgentLoadingOverlay("stop");
        } else {
          // Fallback: Show the overlay directly if the function is not available
          console.log(
            "showAgentLoadingOverlay function not found, using fallback"
          );
          const agentLoadingOverlay = document.getElementById(
            "agentLoadingOverlay"
          );
          if (agentLoadingOverlay) {
            // Update text if elements exist
            const loadingText = agentLoadingOverlay.querySelector(
              ".agent-loading-text"
            );
            if (loadingText) {
              loadingText.textContent = "Stopping Email Agent";
            }

            const loadingSubtext = agentLoadingOverlay.querySelector(
              ".agent-loading-subtext"
            );
            if (loadingSubtext) {
              loadingSubtext.textContent =
                "Please wait while the email assistant is shutting down...";
            }

            agentLoadingOverlay.classList.add("show");
          }
        }

        fetch("/stop", { method: "POST" })
          .then((response) => response.json())
          .then((data) => {
            console.log("Stop agent response:", data);

            // Update status immediately to reflect the change
            updateStatus();

            // Always show a notification for the action
            if (
              data.status.includes("Failed") ||
              data.status.includes("Error")
            ) {
              showToast("Agent Stop Failed", data.status, "danger");

              // Hide loading overlay on error
              if (typeof hideAgentLoadingOverlay === "function") {
                hideAgentLoadingOverlay();
              } else {
                // Fallback: Hide the overlay directly if the function is not available
                const agentLoadingOverlay = document.getElementById(
                  "agentLoadingOverlay"
                );
                if (agentLoadingOverlay) {
                  agentLoadingOverlay.classList.remove("show");
                }
              }

              // Re-enable buttons according to current state
              stopBtn.disabled = false;
              startBtn.disabled = true;
            } else {
              showToast(
                "Agent Stopping",
                "Agent stop requested successfully",
                "warning"
              );

              // Keep the loading overlay visible until status changes to stopped
              // The overlay will be hidden by the updateStatus function once agent is stopped

              // Check status again after a short delay
              setTimeout(() => {
                console.log(
                  "Performing additional status check after agent stop"
                );
                updateStatus();
              }, 3000);
            }

            // Update logs immediately to show the latest status
            updateLogs();
          })
          .catch((error) => {
            console.error("Error stopping agent:", error);

            // Re-enable button on error
            stopBtn.disabled = false;
            startBtn.disabled = false;

            // Hide loading overlay on error
            if (typeof hideAgentLoadingOverlay === "function") {
              hideAgentLoadingOverlay();
            } else {
              // Fallback: Hide the overlay directly if the function is not available
              const agentLoadingOverlay = document.getElementById(
                "agentLoadingOverlay"
              );
              if (agentLoadingOverlay) {
                agentLoadingOverlay.classList.remove("show");
              }
            }

            showToast(
              "Error",
              "Failed to stop agent: " + (error.message || "Unknown error"),
              "danger"
            );
          });
      });

      // Initialize logs panel functionality
      let autoScrollEnabled = true;
      let currentLogFilter = "all";
      let searchTerm = "";
      let logsVisible = false;

      // Initialize the logs panel
      const logsPanel = document.getElementById("all-logs");
      const logsCollapsiblePanel = document.getElementById(
        "logsCollapsiblePanel"
      );
      const collapseLogsBtn = document.getElementById("collapseLogsBtn");

      // Set up event listeners when DOM is loaded
      document.addEventListener("DOMContentLoaded", function () {
        // Set up event listeners for log filtering
        setupLogFilters();

        // Set up collapse button functionality
        if (collapseLogsBtn) {
          collapseLogsBtn.addEventListener("click", function () {
            // Toggle the collapse panel
            const bsCollapse = new bootstrap.Collapse(logsCollapsiblePanel);
            bsCollapse.hide();
          });
        }

        // Set up refresh button functionality
        const refreshLogsBtn = document.getElementById("refreshLogsBtn");
        if (refreshLogsBtn) {
          refreshLogsBtn.addEventListener("click", function () {
            updateLogsPanel(lastLogsData || []);
          });
        }

        // Listen for collapse/expand events
        if (logsCollapsiblePanel) {
          logsCollapsiblePanel.addEventListener(
            "shown.bs.collapse",
            function () {
              logsVisible = true;
              // Update logs when panel is shown
              updateLogsPanel(lastLogsData || []);
              // Change button icon
              if (collapseLogsBtn) {
                collapseLogsBtn.innerHTML = '<i class="bi bi-chevron-up"></i>';
              }
            }
          );

          logsCollapsiblePanel.addEventListener(
            "hidden.bs.collapse",
            function () {
              logsVisible = false;
              // Change button icon
              if (collapseLogsBtn) {
                collapseLogsBtn.innerHTML =
                  '<i class="bi bi-chevron-down"></i>';
              }
            }
          );
        }
      });

      // Function to set up log filter buttons and search
      function setupLogFilters() {
        // Filter buttons
        const filterButtons = document.querySelectorAll("[data-log-filter]");
        filterButtons.forEach((button) => {
          button.addEventListener("click", function () {
            // Remove active class from all buttons
            filterButtons.forEach((btn) => btn.classList.remove("active"));

            // Add active class to clicked button
            this.classList.add("active");

            // Set current filter
            currentLogFilter = this.dataset.logFilter;

            // Apply filters
            applyLogFilters();
          });
        });

        // Search input
        const searchInput = document.getElementById("logs-search");
        if (searchInput) {
          searchInput.addEventListener("input", function () {
            searchTerm = this.value.toLowerCase();
            applyLogFilters();
          });
        }

        // Clear search button
        const clearSearchBtn = document.getElementById("clear-logs-search");
        if (clearSearchBtn) {
          clearSearchBtn.addEventListener("click", function () {
            const searchInput = document.getElementById("logs-search");
            if (searchInput) {
              searchInput.value = "";
              searchTerm = "";
              applyLogFilters();
            }
          });
        }

        // Reset filters button
        const resetFiltersBtn = document.getElementById("clear-filters-btn");
        if (resetFiltersBtn) {
          resetFiltersBtn.addEventListener("click", function () {
            // Reset search
            const searchInput = document.getElementById("logs-search");
            if (searchInput) searchInput.value = "";
            searchTerm = "";

            // Reset filter buttons
            filterButtons.forEach((btn) => btn.classList.remove("active"));
            const allFilterBtn = document.querySelector(
              '[data-log-filter="all"]'
            );
            if (allFilterBtn) allFilterBtn.classList.add("active");
            currentLogFilter = "all";

            // Apply filters
            applyLogFilters();
          });
        }

        // Auto-scroll toggle
        const autoScrollToggle = document.getElementById("auto-scroll-toggle");
        if (autoScrollToggle) {
          autoScrollToggle.addEventListener("click", function () {
            autoScrollEnabled = !autoScrollEnabled;
            this.innerHTML = autoScrollEnabled
              ? '<i class="bi bi-arrow-down-circle"></i> Auto-scroll is ON'
              : '<i class="bi bi-arrow-down-circle-fill"></i> Auto-scroll is OFF';

            // If turning auto-scroll back on, scroll to bottom
            if (autoScrollEnabled && modalLogsPanel) {
              modalLogsPanel.scrollTop = modalLogsPanel.scrollHeight;
            }
          });
        }
      }

      // Function to apply filters to log entries
      function applyLogFilters() {
        const logEntries = document.querySelectorAll(".log-entry");
        let visibleCount = 0;

        logEntries.forEach((entry) => {
          // Start with entry visible
          let visible = true;

          // Apply type filter
          if (
            currentLogFilter !== "all" &&
            entry.dataset.logType !== currentLogFilter
          ) {
            visible = false;
          }

          // Apply search filter if there's a search term
          if (visible && searchTerm) {
            const logText = entry.textContent.toLowerCase();
            visible = logText.includes(searchTerm);
          }

          // Show or hide based on filters
          entry.style.display = visible ? "" : "none";

          // Count visible entries
          if (visible) visibleCount++;
        });

        // Update count display
        const logsCountDisplay = document.getElementById("logs-count-display");
        if (logsCountDisplay) {
          logsCountDisplay.textContent = visibleCount.toString();
        }
      }

      // Function to load automated emails
      function loadAutomatedEmails() {
        fetch("/automated_emails")
          .then((response) => response.json())
          .then((data) => {
            const container = document.getElementById(
              "automated-emails-container"
            );
            container.innerHTML = "";

            if (data.emails && data.emails.length > 0) {
              // Create a table to display the emails
              const table = document.createElement("table");
              table.className = "table table-hover table-responsive-card";

              // Create table header
              const thead = document.createElement("thead");
              thead.innerHTML = `
                          <tr>
                              <th>Email Address</th>
                              <th>Added On</th>
                              <th>Actions</th>
                          </tr>
                      `;
              table.appendChild(thead);

              // Create table body
              const tbody = document.createElement("tbody");
              data.emails.forEach((email) => {
                const tr = document.createElement("tr");

                // Format the date
                const createdDate = new Date(email.created_at);
                const formattedDate =
                  createdDate.toLocaleDateString() +
                  " " +
                  createdDate.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  });

                tr.innerHTML = `
                              <td data-label="Email Address">${email.email_address}</td>
                              <td data-label="Added On">${formattedDate}</td>
                              <td data-label="Actions">
                                  <button class="btn btn-sm btn-danger delete-email" data-id="${email.id}" data-email="${email.email_address}">
                                      <i class="bi bi-trash"></i>
                                      <span class="d-none d-md-inline ms-1">Remove</span>
                                  </button>
                              </td>
                          `;
                tbody.appendChild(tr);
              });
              table.appendChild(tbody);
              container.appendChild(table);

              // Add a counter badge
              const countBadge = document.createElement("div");
              countBadge.className = "mt-2 text-muted small";
              countBadge.innerHTML = `<i class="bi bi-info-circle me-1"></i> Showing ${
                data.emails.length
              } email address${data.emails.length !== 1 ? "es" : ""}`;
              container.appendChild(countBadge);

              // Add event listeners to delete buttons
              document.querySelectorAll(".delete-email").forEach((button) => {
                button.addEventListener("click", function () {
                  const emailId = this.getAttribute("data-id");
                  const emailAddress = this.getAttribute("data-email");
                  if (
                    confirm(
                      `Are you sure you want to remove ${emailAddress} from automated emails?`
                    )
                  ) {
                    deleteAutomatedEmail(emailId);
                  }
                });
              });
            } else {
              // No emails found
              container.innerHTML = `
                          <div class="alert alert-info">
                              <i class="bi bi-info-circle me-2"></i>
                              No automated email addresses configured. All unread emails will be processed.
                          </div>
                      `;
            }
          })
          .catch((error) => {
            console.error("Error loading automated emails:", error);
            document.getElementById("automated-emails-container").innerHTML = `
                      <div class="alert alert-danger">
                          <i class="bi bi-exclamation-triangle me-2"></i>
                          Error loading automated emails. Please try refreshing the page.
                      </div>
                  `;
          });
      }

      // Enhanced function to add a new automated email
      function addAutomatedEmail(emailAddress) {
        const saveBtn = document.getElementById("saveEmailBtn");
        const btnText = saveBtn.querySelector(".btn-text");
        const btnSpinner = saveBtn.querySelector(".btn-spinner");
        const errorDiv = document.getElementById("emailFormError");
        const successDiv = document.getElementById("emailFormSuccess");
        const errorMessage = document.getElementById("errorMessage");

        // Show loading state
        saveBtn.disabled = true;
        btnText.classList.add("d-none");
        btnSpinner.classList.remove("d-none");

        // Hide previous messages
        errorDiv.classList.add("d-none");
        successDiv.classList.add("d-none");

        fetch("/automated_emails", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email_address: emailAddress }),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.error) {
              // Show error message
              errorMessage.textContent = data.error;
              errorDiv.classList.remove("d-none");

              // Reset button state
              saveBtn.disabled = false;
              btnText.classList.remove("d-none");
              btnSpinner.classList.add("d-none");
            } else {
              // Show success message briefly
              successDiv.classList.remove("d-none");

              // Wait a moment to show success, then close modal
              setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(
                  document.getElementById("addEmailModal")
                );
                modal.hide();
                loadAutomatedEmails();

                // Reset form and button state
                resetEmailForm();
              }, 1000);
            }
          })
          .catch((error) => {
            console.error("Error adding automated email:", error);
            errorMessage.textContent =
              "An error occurred while adding the email address. Please try again.";
            errorDiv.classList.remove("d-none");

            // Reset button state
            saveBtn.disabled = false;
            btnText.classList.remove("d-none");
            btnSpinner.classList.add("d-none");
          });
      }

      // Enhanced function to reset the email form
      function resetEmailForm() {
        const saveBtn = document.getElementById("saveEmailBtn");
        const btnText = saveBtn.querySelector(".btn-text");
        const btnSpinner = saveBtn.querySelector(".btn-spinner");

        document.getElementById("emailAddress").value = "";
        document.getElementById("emailFormError").classList.add("d-none");
        document.getElementById("emailFormSuccess").classList.add("d-none");

        // Reset button state
        saveBtn.disabled = false;
        btnText.classList.remove("d-none");
        btnSpinner.classList.add("d-none");
      }

      // Function to delete an automated email
      function deleteAutomatedEmail(emailId) {
        fetch(`/automated_emails/${emailId}`, {
          method: "DELETE",
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.error) {
              alert(data.error);
            } else {
              // Reload the emails list
              loadAutomatedEmails();
            }
          })
          .catch((error) => {
            console.error("Error deleting automated email:", error);
            alert(
              "An error occurred while deleting the email. Please try again."
            );
          });
      }

      // Enhanced event listener for the save button in the modal
      document
        .getElementById("saveEmailBtn")
        .addEventListener("click", function () {
          const emailAddress = document
            .getElementById("emailAddress")
            .value.trim();
          if (emailAddress) {
            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (emailRegex.test(emailAddress)) {
              addAutomatedEmail(emailAddress);
            } else {
              const errorDiv = document.getElementById("emailFormError");
              const errorMessage = document.getElementById("errorMessage");
              errorMessage.textContent =
                "Please enter a valid email address format.";
              errorDiv.classList.remove("d-none");
            }
          } else {
            const errorDiv = document.getElementById("emailFormError");
            const errorMessage = document.getElementById("errorMessage");
            errorMessage.textContent = "Please enter an email address.";
            errorDiv.classList.remove("d-none");
          }
        });

      // Enhanced form validation on input
      document
        .getElementById("emailAddress")
        .addEventListener("input", function () {
          const errorDiv = document.getElementById("emailFormError");
          const successDiv = document.getElementById("emailFormSuccess");

          // Hide error messages when user starts typing
          errorDiv.classList.add("d-none");
          successDiv.classList.add("d-none");
        });

      // Enhanced modal reset when closed
      document
        .getElementById("addEmailModal")
        .addEventListener("hidden.bs.modal", function () {
          resetEmailForm();
        });

      // Function to update pending email replies count
      function updatePendingRepliesCount() {
        fetch("/api/email_replies")
          .then((response) => response.json())
          .then((data) => {
            const pendingRepliesCount = document.getElementById(
              "pending-replies-count"
            );
            if (pendingRepliesCount) {
              pendingRepliesCount.textContent = data.replies
                ? data.replies.length
                : "0";
            }
          })
          .catch((error) => {
            console.error("Error fetching pending replies count:", error);
          });
      }

      // Update status, logs, stats, and pending replies immediately and then periodically
      updateStatus();
      updateLogs();
      updateStats();
      loadAutomatedEmails();
      updatePendingRepliesCount();
      setInterval(updateStatus, 3000);
      setInterval(updateLogs, 3000);
      setInterval(updateStats, 5000);
      setInterval(updatePendingRepliesCount, 10000);
    </script>
  </body>
</html>
