# Email Reply Agent with Google API

A Python Flask web application that automates Gmail replies using Google OAuth 2.0 and OpenRouter API. The application features a modern, responsive UI built with Bootstrap 5.

## Features

- **Google OAuth 2.0 Integration**: Securely authenticate with Gmail
- **Email Automation**: Automatically check and reply to unread emails
- **AI-Powered Replies**: Generate intelligent responses using OpenRouter API
- **Background Agent**: Process emails every 10 seconds
- **Modern UI**: Clean, responsive design with Boots<PERSON><PERSON> 5
- **Real-time Updates**: Live status and log updates

## Setup Instructions

### Prerequisites

- Python 3.8 or higher
- A Google Cloud Platform account
- An OpenRouter API key

### Google API Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project
3. Enable the Gmail API
4. Configure the OAuth consent screen
5. Create OAuth 2.0 credentials (Web application type)
6. Add authorized redirect URIs: `http://localhost:5000/auth/callback`
7. Download the credentials JSON file and save it as `credentials.json` in the project root

### Installation

1. Clone the repository or download the source code
2. Create a virtual environment:
   ```
   python -m venv myenv
   ```
3. Activate the virtual environment:
   - Windows: `myenv\Scripts\activate`
   - macOS/Linux: `source myenv/bin/activate`
4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
5. Set environment variables (optional):
   - `OPENROUTER_API_KEY`: Your OpenRouter API key
   - `FLASK_SECRET_KEY`: A secure random string for Flask sessions

### Running the Application

#### Local Development

1. Start the Flask server:
   ```
   python app.py
   ```
2. Open your browser and navigate to: `https://localhost:5000`
3. Sign in with your Google account
4. Use the dashboard to start/stop the email reply agent

#### Production Deployment (Render)

1. Create a Render account at https://render.com

2. Connect your GitHub repository

3. Create a new Web Service:

   - Select your repository
   - Choose Python environment
   - Set the following environment variables in Render dashboard:
     ```
     FLASK_ENV=production
     FLASK_APP=app.py
     FLASK_SECRET_KEY=(generate a secure random key)
     GOOGLE_CLIENT_ID=(from Google Cloud Console)
     GOOGLE_CLIENT_SECRET=(from Google Cloud Console)
     OPENROUTER_API_KEY=(your OpenRouter API key)
     HTTPS_ENABLED=true
     ```

4. Update OAuth credentials:

   - Go to Google Cloud Console
   - Add your Render domain to authorized domains
   - Update OAuth redirect URIs to include:
     `https://your-app-name.onrender.com/auth/callback`

5. Deploy:
   - Render will automatically deploy your application
   - Monitor the deployment logs in Render dashboard

## Usage

1. After signing in, you'll be redirected to the dashboard
2. Click the "Start Agent" button to begin processing emails
3. The agent will check for unread emails every 10 seconds
4. For each unread email, it will:
   - Generate a reply using OpenRouter API
   - Send the reply if appropriate (skip spam)
   - Mark the email as read
5. Monitor the agent's activity in the logs panel
6. Click "Stop Agent" to pause email processing

## Database Setup

The application uses Railway PostgreSQL for reliable, free database hosting.

### Quick Setup with Railway (Recommended)

1. Create a free Railway account at [railway.app](https://railway.app)
2. Create a new PostgreSQL database
3. Copy the DATABASE_URL from Railway
4. Update your `.env` file with the Railway DATABASE_URL
5. Run `python init_railway_db.py` to initialize the database

See [RAILWAY_SETUP.md](RAILWAY_SETUP.md) for detailed instructions.

## Security Notes

- This application stores OAuth tokens in the Flask session
- For production use, implement a secure database for token storage
- Never commit your `credentials.json` file to public repositories
- Use environment variables for sensitive keys

## License

MIT
