/* Email Preview Styles */
:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --secondary-color: #6b7280;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  --light-color: #f3f4f6;
  --dark-color: #1f2937;
  --border-color: #e5e7eb;
  --text-color: #374151;
  --text-muted: #6b7280;
  --bg-color: #ffffff;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --transition: all 0.3s ease;
  --border-radius: 0.5rem;
  --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

/* Email Preview Container */
.email-preview-container {
  font-family: var(--font-family);
  color: var(--text-color);
  max-width: 100%;
  margin: 0 auto;
}

/* Email Card */
.email-card {
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.email-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Email Header */
.email-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--light-color);
}

.email-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--dark-color);
}

.email-meta {
  margin-top: 0.75rem;
  font-size: 0.875rem;
}

.email-meta-item {
  margin-bottom: 0.5rem;
  display: flex;
}

.email-meta-label {
  font-weight: 600;
  width: 4rem;
  color: var(--text-muted);
}

.email-meta-value {
  flex: 1;
}

/* Email Body */
.email-body {
  padding: 1.5rem;
  white-space: pre-wrap;
  font-size: 0.95rem;
  line-height: 1.6;
  max-height: 300px;
  overflow-y: auto;
  background-color: var(--bg-color);
  border-bottom: 1px solid var(--border-color);
}

/* Reply Section */
.reply-section {
  padding: 1.5rem;
  background-color: var(--light-color);
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.reply-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--dark-color);
}

.reply-status {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-weight: 500;
}

.reply-status-pending {
  background-color: var(--warning-color);
  color: white;
}

.reply-status-sent {
  background-color: var(--success-color);
  color: white;
}

.reply-status-discarded {
  background-color: var(--secondary-color);
  color: white;
}

.reply-content {
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.25rem;
  white-space: pre-wrap;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.reply-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* No Reply Needed */
.no-reply-needed {
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
  border-radius: var(--border-radius);
  padding: 1.25rem;
  margin-bottom: 1.5rem;
}

.no-reply-needed-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  color: var(--danger-color);
}

.no-reply-needed-header i {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.no-reply-needed-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.no-reply-needed-message {
  color: var(--text-color);
  font-size: 0.95rem;
  line-height: 1.6;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: var(--transition);
  cursor: pointer;
  border: none;
  outline: none;
}

.btn i {
  margin-right: 0.5rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #0ca678;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.btn-outline:hover {
  background-color: var(--light-color);
}

/* Editor Modal */
.editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.editor-modal.show {
  opacity: 1;
  visibility: visible;
}

.editor-modal-content {
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  position: relative;
}

.editor-modal-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--dark-color);
}

.editor-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
}

.editor-modal-close:hover {
  color: var(--danger-color);
}

.editor-modal-body {
  padding: 1.5rem;
}

.editor-modal-footer {
  padding: 1.25rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Text Editor */
.text-editor {
  width: 100%;
  min-height: 300px;
  padding: 1rem;
  font-family: var(--font-family);
  font-size: 0.95rem;
  line-height: 1.6;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  resize: vertical;
  transition: var(--transition);
}

.text-editor:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Regenerate Form */
.regenerate-form {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--dark-color);
}

.form-select {
  width: 100%;
  padding: 0.625rem;
  font-size: 0.95rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-color);
  transition: var(--transition);
  color: var(--text-color);
}

/* Style for select options */
.form-select option {
  color: white;
  background-color: #2daa9e;
}

.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.form-textarea {
  width: 100%;
  min-height: 100px;
  padding: 0.625rem;
  font-size: 0.95rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-color);
  transition: var(--transition);
  resize: vertical;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Loading Spinner */
.spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 0.2rem solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Styles */
@media (max-width: 768px) {
  .reply-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .email-meta-item {
    flex-direction: column;
  }

  .email-meta-label {
    width: 100%;
    margin-bottom: 0.25rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --bg-color: #1f2937;
    --light-color: #374151;
    --dark-color: #f9fafb;
    --border-color: #4b5563;
    --text-color: #e5e7eb;
    --text-muted: #9ca3af;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2),
      0 2px 4px -1px rgba(0, 0, 0, 0.1);
  }

  .no-reply-needed {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
  }
}
