#!/usr/bin/env python3
"""
Railway Database Connection Test
===============================

Quick test script to verify your Railway database connection.
"""

import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_railway_connection():
    """Test connection to Railway PostgreSQL database"""
    print("🚂 Testing Railway Database Connection...")
    print("=" * 50)
    
    # Get database URL
    database_url = os.getenv('DATABASE_URL')
    
    if not database_url:
        print("❌ ERROR: DATABASE_URL not found in .env file")
        print("Please add your Railway DATABASE_URL to the .env file")
        return False
    
    # Hide password in output
    if '@' in database_url and database_url.count('@') >= 1:
        safe_url = database_url.split('@')[0] + ':****@' + database_url.split('@')[1]
    else:
        # Handle malformed URLs
        safe_url = database_url[:20] + "****" + database_url[-20:] if len(database_url) > 40 else "****"
    print(f"🔗 Database URL: {safe_url}")

    # Check if URL looks like a proper PostgreSQL URL
    if not database_url.startswith('postgresql://'):
        print("⚠️  WARNING: DATABASE_URL doesn't start with 'postgresql://'")
        print("Expected format: postgresql://username:password@host:port/database")
        print("Current value:", database_url)
        return False
    
    try:
        # Test connection
        print("🔍 Attempting connection...")
        conn = psycopg2.connect(database_url)
        
        # Test query
        cur = conn.cursor()
        cur.execute('SELECT version();')
        version = cur.fetchone()[0]
        
        print(f"✅ Connection successful!")
        print(f"📊 PostgreSQL version: {version}")
        
        # Test basic operations
        cur.execute('SELECT current_database();')
        db_name = cur.fetchone()[0]
        print(f"🗄️  Database name: {db_name}")
        
        # Close connection
        cur.close()
        conn.close()
        
        print("\n🎉 Railway database is ready to use!")
        return True
        
    except psycopg2.OperationalError as e:
        print(f"❌ Connection failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check your Railway DATABASE_URL is correct")
        print("2. Ensure your Railway database is running")
        print("3. Check your internet connection")
        return False
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_railway_connection()
    
    if success:
        print("\n✅ Next step: Run 'python init_railway_db.py' to set up tables")
    else:
        print("\n❌ Please fix the connection issues before proceeding")
