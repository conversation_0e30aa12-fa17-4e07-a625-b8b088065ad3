{% extends 'base.html' %} {% block title %}Admin Dashboard{% endblock %} {%
block content %}
<style>
  /* Enhanced Admin Dashboard Styles */
  .admin-dashboard {
    background: linear-gradient(135deg, #06202b 0%, #077a7d 100%);
    min-height: 100vh;
    padding: 2rem 0;
  }

  .admin-card {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(245, 238, 221, 0.9) 100%
    );
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  }

  .admin-card-header {
    background: linear-gradient(135deg, #077a7d 0%, #06595b 100%);
    color: white;
    border-radius: 20px 20px 0 0;
    padding: 1.5rem;
    border: none;
  }

  .admin-tool-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(7, 122, 125, 0.1);
    height: 100%;
  }

  .admin-tool-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 1);
  }

  .admin-tool-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #077a7d 0%, #2daa9e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
  }

  .admin-tool-card:hover .admin-tool-icon {
    transform: scale(1.1) rotate(5deg);
  }

  .admin-btn {
    background: linear-gradient(135deg, #077a7d 0%, #06595b 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    min-width: 150px;
  }

  .admin-btn:hover {
    background: linear-gradient(135deg, #06595b 0%, #077a7d 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(7, 122, 125, 0.3);
  }

  @media (max-width: 768px) {
    .admin-dashboard {
      padding: 1rem 0;
    }

    .admin-tool-card {
      padding: 1.5rem;
      margin-bottom: 1rem;
    }

    .admin-tool-icon {
      width: 60px;
      height: 60px;
      font-size: 1.5rem;
    }
  }
</style>

<div class="admin-dashboard">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="admin-card">
          <div class="admin-card-header">
            <h4 class="mb-0 d-flex align-items-center">
              <i class="fas fa-shield-alt me-3"></i>
              Admin Dashboard
            </h4>
          </div>
          <div class="card-body p-4">
            <div class="row">
              <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-tool-card">
                  <div class="admin-tool-icon">
                    <i class="fas fa-database"></i>
                  </div>
                  <h5
                    class="card-title mb-3"
                    style="color: #06202b; font-weight: 700"
                  >
                    Database Viewer
                  </h5>
                  <p class="card-text mb-4" style="color: #495057">
                    View and query database tables with an intuitive interface
                  </p>
                  <a href="{{ url_for('admin_database') }}" class="admin-btn">
                    <i class="fas fa-eye me-2"></i>
                    Access Database
                  </a>
                </div>
              </div>

              <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-tool-card">
                  <div class="admin-tool-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <h5
                    class="card-title mb-3"
                    style="color: #06202b; font-weight: 700"
                  >
                    User Management
                  </h5>
                  <p class="card-text mb-4" style="color: #495057">
                    Manage user accounts and permissions
                  </p>
                  <button class="admin-btn" disabled>
                    <i class="fas fa-cog me-2"></i>
                    Coming Soon
                  </button>
                </div>
              </div>

              <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-tool-card">
                  <div class="admin-tool-icon">
                    <i class="fas fa-chart-bar"></i>
                  </div>
                  <h5
                    class="card-title mb-3"
                    style="color: #06202b; font-weight: 700"
                  >
                    Analytics
                  </h5>
                  <p class="card-text mb-4" style="color: #495057">
                    View system analytics and usage statistics
                  </p>
                  <button class="admin-btn" disabled>
                    <i class="fas fa-chart-line me-2"></i>
                    Coming Soon
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
