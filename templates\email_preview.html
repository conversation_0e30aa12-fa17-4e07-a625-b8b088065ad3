<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    {% set seo_data = seo or get_seo_data('email_preview') %}
    <title>{{ seo_data.title }}</title>
    <meta name="description" content="{{ seo_data.description }}" />
    <meta name="keywords" content="{{ seo_data.keywords }}" />
    <meta name="author" content="Email Reply Agent" />
    <meta name="robots" content="noindex, nofollow" />
    <link rel="canonical" href="{{ get_canonical_url('email_preview') }}" />

    <!-- Enhanced Favicon Implementation -->
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="{{ url_for('static', filename='img/favicon-16x16.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="{{ url_for('static', filename='img/favicon-32x32.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="48x48"
      href="{{ url_for('static', filename='img/favicon-48x48.png') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="{{ url_for('static', filename='img/apple-touch-icon.png') }}"
    />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='manifest.json') }}"
    />
    <meta name="theme-color" content="#1E90FF" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/styles.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard-enhanced.css') }}"
    />
    <!-- SweetAlert2 CSS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css"
    />
    <style>
      /* Toast container */
      #toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1060;
        max-width: 350px;
      }

      /* Email sending overlay */
      .email-sending-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10;
        border-radius: 8px;
      }

      .email-sending-content {
        text-align: center;
        padding: 20px;
      }

      /* Success animation */
      .email-success-animation,
      .email-discard-animation {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      /* Success checkmark animation */
      .success-checkmark {
        width: 80px;
        height: 80px;
        margin: 0 auto;
        position: relative;
      }

      .check-icon {
        width: 80px;
        height: 80px;
        position: relative;
        border-radius: 50%;
        box-sizing: content-box;
        border: 4px solid #4caf50;
      }

      .check-icon::before {
        top: 3px;
        left: -2px;
        width: 30px;
        transform-origin: 100% 50%;
        border-radius: 100px 0 0 100px;
      }

      .check-icon::after {
        top: 0;
        left: 30px;
        width: 60px;
        transform-origin: 0 50%;
        border-radius: 0 100px 100px 0;
        animation: rotate-circle 4.25s ease-in;
      }

      .check-icon::before,
      .check-icon::after {
        content: "";
        height: 100px;
        position: absolute;
        background: #f8f9fa;
        transform: rotate(-45deg);
      }

      .check-icon .icon-line {
        height: 5px;
        background-color: #4caf50;
        display: block;
        border-radius: 2px;
        position: absolute;
        z-index: 10;
      }

      .check-icon .icon-line.line-tip {
        top: 46px;
        left: 14px;
        width: 25px;
        transform: rotate(45deg);
        animation: icon-line-tip 0.75s;
      }

      .check-icon .icon-line.line-long {
        top: 38px;
        right: 8px;
        width: 47px;
        transform: rotate(-45deg);
        animation: icon-line-long 0.75s;
      }

      @keyframes icon-line-tip {
        0% {
          width: 0;
          left: 1px;
          top: 19px;
        }
        54% {
          width: 0;
          left: 1px;
          top: 19px;
        }
        70% {
          width: 50px;
          left: -8px;
          top: 37px;
        }
        84% {
          width: 17px;
          left: 21px;
          top: 48px;
        }
        100% {
          width: 25px;
          left: 14px;
          top: 46px;
        }
      }

      @keyframes icon-line-long {
        0% {
          width: 0;
          right: 46px;
          top: 54px;
        }
        65% {
          width: 0;
          right: 46px;
          top: 54px;
        }
        84% {
          width: 55px;
          right: 0px;
          top: 35px;
        }
        100% {
          width: 47px;
          right: 8px;
          top: 38px;
        }
      }

      /* Modal saving/regenerating state */
      .editor-modal-content.saving,
      .editor-modal-content.regenerating {
        position: relative;
      }

      .editor-modal-content.saving::after,
      .editor-modal-content.regenerating::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        color: #f8f9fa;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        z-index: 10;
        border-radius: 8px;
        pointer-events: none;
      }

      /* Spinner animation */
      .spinner {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body class="dashboard-body">
    <!-- Toast Container for Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Topbar -->
    <nav id="topbar">
      <button class="btn" id="sidebarToggle" type="button">
        <i class="bi bi-list"></i>
      </button>
      <a class="navbar-brand" href="{{ url_for('dashboard') }}">
        <i class="bi bi-envelope-paper-heart"></i>
        Email Reply Agent
      </a>
      <div class="topbar-actions">
        <div class="user-info">
          <div class="user-avatar">{{ current_user.name[0] }}</div>
          <div class="user-name">{{ current_user.name }}</div>
        </div>
        <a href="{{ url_for('logout') }}" class="btn-logout">
          <i class="bi bi-box-arrow-right"></i>Logout
        </a>
      </div>
    </nav>

    <!-- Sidebar Overlay (for mobile) -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <div class="sidebar">
      <div class="sidebar-content">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a href="{{ url_for('dashboard') }}" class="nav-link">
              <i class="bi bi-speedometer2"></i>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('email_preview') }}" class="nav-link active">
              <i class="bi bi-envelope"></i>
              Email Replies
              <span class="badge" id="pending-replies-count">0</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('profile') }}" class="nav-link">
              <i class="bi bi-person"></i>
              Profile
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('settings') }}" class="nav-link">
              <i class="bi bi-gear"></i>
              Settings
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Content -->
    <main class="content">
      <div class="container-fluid">
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %} {% for category, message in messages %}
        <div
          class="alert alert-{{ category }} alert-dismissible fade show"
          role="alert"
        >
          {{ message }}
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="alert"
            aria-label="Close"
          ></button>
        </div>
        {% endfor %} {% endif %} {% endwith %}

        <div class="row mb-4">
          <div class="col-12">
            <div class="dashboard-card">
              <div class="card-header">
                <h5>Email Replies</h5>
                <div class="d-flex gap-2">
                  <button class="action-btn btn-danger" id="discardAllBtn">
                    <i class="bi bi-trash"></i>
                    <span class="d-none d-sm-inline">Discard All</span>
                  </button>
                  <button class="action-btn btn-primary" id="refreshRepliesBtn">
                    <i class="bi bi-arrow-clockwise"></i>
                    <span class="d-none d-sm-inline">Refresh</span>
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div
                  class="alert alert-info alert-dismissible fade show mb-3"
                  role="alert"
                >
                  <i class="bi bi-info-circle"></i>
                  <span
                    >Review and manage your AI-generated email replies before
                    sending them.</span
                  >
                  <button
                    type="button"
                    class="btn-close"
                    data-bs-dismiss="alert"
                  ></button>
                </div>
                <div
                  id="email-replies-container"
                  class="email-preview-container"
                >
                  <div class="d-flex justify-content-center py-4">
                    <div
                      class="spinner-border"
                      style="color: var(--primary)"
                      role="status"
                    >
                      <span class="visually-hidden">Loading...</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Email Editor Modal -->
    <div class="email-editor-modal" id="emailEditorModal">
      <div class="email-editor-container">
        <div class="email-editor-header">
          <h4>Edit Email Reply</h4>
          <button type="button" class="email-editor-close" id="closeEditorBtn">
            <i class="bi bi-x-lg"></i>
          </button>
        </div>
        <div class="email-editor-body">
          <div class="form-group">
            <label for="emailEditor" class="form-label">Edit your reply:</label>
            <textarea id="emailEditor" class="email-editor-textarea"></textarea>
          </div>
        </div>
        <div class="email-editor-footer">
          <button
            type="button"
            class="action-btn btn-outline-primary"
            id="cancelEditBtn"
          >
            <i class="bi bi-x-circle"></i> Cancel
          </button>
          <button type="button" class="action-btn btn-primary" id="saveEditBtn">
            <i class="bi bi-check-circle"></i> Save Changes
          </button>
        </div>
      </div>
    </div>

    <!-- Regenerate Modal -->
    <div class="regenerate-modal" id="regenerateModal">
      <div class="regenerate-container">
        <div class="regenerate-header">
          <h4>Regenerate Email Reply</h4>
          <button
            type="button"
            class="regenerate-close"
            id="closeRegenerateBtn"
          >
            <i class="bi bi-x-lg"></i>
          </button>
        </div>
        <div class="regenerate-body">
          <div class="regenerate-options">
            <div class="form-group">
              <label for="toneSelect" class="form-label">Tone:</label>
              <select id="toneSelect" class="form-control">
                <option value="formal">Formal</option>
                <option value="professional" selected>Professional</option>
                <option value="friendly">Friendly</option>
                <option value="casual">Casual</option>
                <option value="concise">Concise</option>
              </select>
            </div>
            <div class="form-group">
              <label for="lengthSelect" class="form-label">Length:</label>
              <select id="lengthSelect" class="form-control">
                <option value="short">Short</option>
                <option value="medium" selected>Medium</option>
                <option value="long">Long</option>
              </select>
            </div>
            <div class="form-group">
              <label for="customInstructions" class="form-label"
                >Custom Instructions (Optional):</label
              >
              <textarea
                id="customInstructions"
                class="form-control"
                placeholder="E.g., Mention my availability next week"
                rows="4"
              ></textarea>
            </div>
          </div>
        </div>
        <div class="regenerate-footer">
          <button
            type="button"
            class="action-btn btn-outline-primary"
            id="cancelRegenerateBtn"
          >
            <i class="bi bi-x-circle"></i> Cancel
          </button>
          <button
            type="button"
            class="action-btn btn-primary"
            id="confirmRegenerateBtn"
          >
            <span id="regenerateSpinner" class="spinner d-none"></span>
            <span id="regenerateText"
              ><i class="bi bi-arrow-repeat"></i> Regenerate Reply</span
            >
          </button>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="{{ url_for('static', filename='js/unified-dashboard.js') }}"></script>
    <script>
      // Email Preview Functionality
      document.addEventListener("DOMContentLoaded", function () {
        // DOM Elements
        const emailRepliesContainer = document.getElementById(
          "email-replies-container"
        );
        const pendingRepliesCount = document.getElementById(
          "pending-replies-count"
        );
        const refreshRepliesBtn = document.getElementById("refreshRepliesBtn");

        // Editor Modal Elements
        const emailEditorModal = document.getElementById("emailEditorModal");
        const emailEditor = document.getElementById("emailEditor");
        const closeEditorBtn = document.getElementById("closeEditorBtn");
        const cancelEditBtn = document.getElementById("cancelEditBtn");
        const saveEditBtn = document.getElementById("saveEditBtn");

        // Regenerate Modal Elements
        const regenerateModal = document.getElementById("regenerateModal");
        const toneSelect = document.getElementById("toneSelect");
        const lengthSelect = document.getElementById("lengthSelect");
        const customInstructions =
          document.getElementById("customInstructions");
        const closeRegenerateBtn =
          document.getElementById("closeRegenerateBtn");
        const cancelRegenerateBtn = document.getElementById(
          "cancelRegenerateBtn"
        );
        const confirmRegenerateBtn = document.getElementById(
          "confirmRegenerateBtn"
        );
        const regenerateSpinner = document.getElementById("regenerateSpinner");
        const regenerateText = document.getElementById("regenerateText");

        // Current reply being edited
        let currentReplyId = null;

        // Fetch with retry and exponential backoff
        async function fetchWithRetry(url, options = {}, maxRetries = 3) {
          let retries = 0;
          let lastError;

          while (retries < maxRetries) {
            try {
              const response = await fetch(url, options);

              // If we get a 429 Too Many Requests, retry after a delay
              if (response.status === 429) {
                const retryAfter =
                  response.headers.get("Retry-After") ||
                  Math.pow(2, retries) * 1000;
                console.log(
                  `Rate limited. Retrying after ${retryAfter}ms (retry ${
                    retries + 1
                  }/${maxRetries})`
                );
                await new Promise((resolve) => setTimeout(resolve, retryAfter));
                retries++;
                continue;
              }

              if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
              }

              return await response.json();
            } catch (error) {
              lastError = error;

              // Only retry on network errors or 429 status
              if (
                error.message.includes("429") ||
                !error.message.includes("HTTP error")
              ) {
                const delay = Math.pow(2, retries) * 1000;
                console.log(
                  `Request failed. Retrying after ${delay}ms (retry ${
                    retries + 1
                  }/${maxRetries})`
                );
                await new Promise((resolve) => setTimeout(resolve, delay));
                retries++;
              } else {
                // Don't retry other HTTP errors
                break;
              }
            }
          }

          // If we've exhausted retries, throw the last error
          throw lastError;
        }

        // Load email replies
        function loadEmailReplies() {
          emailRepliesContainer.innerHTML = `
          <div class="d-flex justify-content-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        `;

          fetchWithRetry("/api/email_replies")
            .then((data) => {
              if (data.error) {
                throw new Error(data.error);
              }

              if (data.replies && data.replies.length > 0) {
                // Update pending replies count
                pendingRepliesCount.textContent = data.replies.length;

                // Sort replies by creation date (newest first)
                data.replies.sort(
                  (a, b) => new Date(b.created_at) - new Date(a.created_at)
                );

                // Clear container
                emailRepliesContainer.innerHTML = "";

                // Add each reply
                data.replies.forEach((reply) => {
                  const replyElement = createReplyElement(reply);
                  emailRepliesContainer.appendChild(replyElement);
                });

                // Add event listeners to buttons
                addEventListeners();
              } else {
                // No replies found
                pendingRepliesCount.textContent = "0";
                emailRepliesContainer.innerHTML = `
                <div class="alert alert-info">
                  <i class="bi bi-info-circle me-2"></i>
                  No pending email replies found. The agent will generate replies when new emails arrive.
                </div>
              `;
              }
            })
            .catch((error) => {
              console.error("Error loading email replies:", error);

              // Determine if this is a rate limiting error
              const isRateLimited =
                error.message && error.message.includes("429");
              const errorMessage = isRateLimited
                ? "Rate limit exceeded. The server is temporarily unavailable due to too many requests. Please try again in a moment."
                : error.message || "Unknown error";

              const alertClass = isRateLimited
                ? "alert-warning"
                : "alert-danger";
              const iconClass = isRateLimited
                ? "bi-hourglass-split"
                : "bi-exclamation-triangle";

              // Show error message with retry button
              emailRepliesContainer.innerHTML = `
              <div class="${alertClass}">
                <div class="d-flex align-items-center justify-content-between">
                  <div>
                    <i class="${iconClass} me-2"></i>
                    Error loading email replies: ${errorMessage}
                  </div>
                  <button class="btn btn-sm btn-outline-${
                    isRateLimited ? "warning" : "danger"
                  }" id="retryLoadBtn">
                    <i class="bi bi-arrow-clockwise me-1"></i>Retry
                  </button>
                </div>
              </div>
            `;

              // Add event listener to retry button
              document
                .getElementById("retryLoadBtn")
                ?.addEventListener("click", loadEmailReplies);

              // Set pending count to question mark to indicate unknown
              pendingRepliesCount.textContent = "?";
            });
        }

        // Create a reply element
        function createReplyElement(reply) {
          const replyElement = document.createElement("div");
          replyElement.className = "email-preview-container";
          replyElement.dataset.replyId = reply.id;

          // Check if this is a "No Reply Needed" case
          if (reply.reply_body === "[NO REPLY NEEDED]") {
            replyElement.innerHTML = `
            <div class="email-card">
              <div class="email-header">
                <h3>${reply.subject}</h3>
                <div class="email-meta">
                  <div class="email-meta-item">
                    <div class="email-meta-label">From:</div>
                    <div class="email-meta-value">${reply.sender}</div>
                  </div>
                </div>
              </div>
              <div class="email-body">${formatEmailBody(
                reply.original_body
              )}</div>
              <div class="reply-section">
                <div class="no-reply-needed">
                  <div class="no-reply-needed-header">
                    <i class="bi bi-info-circle"></i>
                    <h4>No Reply Needed</h4>
                  </div>
                  <p class="no-reply-needed-message">
                    This email appears to be spam or doesn't require a reply.
                  </p>
                  <div class="reply-actions">
                    <button class="btn btn-outline regenerate-btn" data-reply-id="${
                      reply.id
                    }">
                      <i class="bi bi-pencil"></i> Draft a Reply Anyway
                    </button>
                    <button class="btn btn-danger discard-btn" data-reply-id="${
                      reply.id
                    }">
                      <i class="bi bi-trash"></i> Discard
                    </button>
                  </div>
                </div>
              </div>
            </div>
          `;
          } else {
            // Regular reply
            replyElement.innerHTML = `
            <div class="email-card">
              <div class="email-header">
                <h3>${reply.subject}</h3>
                <div class="email-meta">
                  <div class="email-meta-item">
                    <div class="email-meta-label">From:</div>
                    <div class="email-meta-value">${reply.sender}</div>
                  </div>
                </div>
              </div>
              <div class="email-body">${formatEmailBody(
                reply.original_body
              )}</div>
              <div class="reply-section">
                <div class="reply-header">
                  <h4>AI-Generated Reply</h4>
                  <span class="reply-status reply-status-${
                    reply.status
                  }">${capitalizeFirstLetter(reply.status)}</span>
                </div>
                <div class="reply-content">${formatEmailBody(
                  reply.modified_reply || reply.reply_body
                )}</div>
                <div class="reply-actions">
                  <button class="btn btn-success accept-btn" data-reply-id="${
                    reply.id
                  }">
                    <i class="bi bi-check-circle"></i> Accept & Send
                  </button>
                  <button class="btn btn-primary modify-btn" data-reply-id="${
                    reply.id
                  }">
                    <i class="bi bi-pencil"></i> Modify
                  </button>
                  <button class="btn btn-secondary regenerate-btn" data-reply-id="${
                    reply.id
                  }">
                    <i class="bi bi-arrow-repeat"></i> Regenerate
                  </button>
                  <button class="btn btn-outline discard-btn" data-reply-id="${
                    reply.id
                  }">
                    <i class="bi bi-trash"></i> Discard
                  </button>
                </div>
              </div>
            </div>
          `;
          }

          return replyElement;
        }

        // Add event listeners to buttons
        function addEventListeners() {
          // Accept buttons
          document.querySelectorAll(".accept-btn").forEach((button) => {
            button.addEventListener("click", function () {
              const replyId = this.dataset.replyId;
              showAcceptConfirmation(replyId);
            });
          });

          // Modify buttons
          document.querySelectorAll(".modify-btn").forEach((button) => {
            button.addEventListener("click", function () {
              const replyId = this.dataset.replyId;
              openEditor(replyId);
            });
          });

          // Regenerate buttons
          document.querySelectorAll(".regenerate-btn").forEach((button) => {
            button.addEventListener("click", function () {
              const replyId = this.dataset.replyId;
              openRegenerateModal(replyId);
            });
          });

          // Discard buttons
          document.querySelectorAll(".discard-btn").forEach((button) => {
            button.addEventListener("click", function () {
              const replyId = this.dataset.replyId;
              showDiscardConfirmation(replyId);
            });
          });
        }

        // Create a toast notification system
        function createToastContainer() {
          if (!document.getElementById("toast-container")) {
            const toastContainer = document.createElement("div");
            toastContainer.id = "toast-container";
            toastContainer.className = "position-fixed top-0 end-0 p-3";
            toastContainer.style.zIndex = "1050";
            toastContainer.style.maxWidth = "350px";
            document.body.appendChild(toastContainer);
          }
        }

        // Show a toast notification
        function showToast(message, type = "success", duration = 3000) {
          createToastContainer();
          const toastContainer = document.getElementById("toast-container");

          const toastId = "toast-" + Date.now();
          const iconClass =
            type === "success"
              ? "bi-check-circle-fill"
              : type === "error"
              ? "bi-exclamation-triangle-fill"
              : type === "info"
              ? "bi-info-circle-fill"
              : "bi-bell-fill";

          const toast = document.createElement("div");
          toast.className = `toast align-items-center text-white bg-${type} border-0`;
          toast.id = toastId;
          toast.setAttribute("role", "alert");
          toast.setAttribute("aria-live", "assertive");
          toast.setAttribute("aria-atomic", "true");

          toast.innerHTML = `
          <div class="d-flex">
            <div class="toast-body">
              <i class="bi ${iconClass} me-2"></i>
              ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
        `;

          toastContainer.appendChild(toast);

          const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: duration,
          });

          bsToast.show();

          // Remove the toast after it's hidden
          toast.addEventListener("hidden.bs.toast", function () {
            toast.remove();
          });
        }

        // Send a reply with improved UX
        function sendReply(replyId) {
          const button = document.querySelector(
            `.accept-btn[data-reply-id="${replyId}"]`
          );
          const replyElement = document.querySelector(
            `.email-preview-container[data-reply-id="${replyId}"]`
          );

          if (button) {
            button.disabled = true;
            button.innerHTML = '<span class="spinner"></span> Sending...';
          }

          // Add a sending overlay to the email card
          if (replyElement) {
            const overlay = document.createElement("div");
            overlay.className = "email-sending-overlay";
            overlay.innerHTML = `
            <div class="email-sending-content">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Sending...</span>
              </div>
              <h4 class="mt-3">Sending your reply...</h4>
            </div>
          `;
            replyElement.appendChild(overlay);
          }

          fetchWithRetry(`/api/email_replies/${replyId}/send`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
          })
            .then((data) => {
              if (data.status === "success") {
                // Show success message with animation
                if (replyElement) {
                  // Remove the overlay
                  const overlay = replyElement.querySelector(
                    ".email-sending-overlay"
                  );
                  if (overlay) overlay.remove();

                  // Show success animation
                  replyElement.innerHTML = `
                <div class="email-success-animation">
                  <div class="success-checkmark">
                    <div class="check-icon">
                      <span class="icon-line line-tip"></span>
                      <span class="icon-line line-long"></span>
                    </div>
                  </div>
                  <h4 class="text-success text-center mt-3">Email Sent Successfully!</h4>
                </div>
              `;

                  // Show success alert
                  Swal.fire({
                    title: "Success!",
                    text: "Email sent successfully!",
                    icon: "success",
                    confirmButtonColor: "#2DAA9E",
                    confirmButtonText: "OK",
                    timer: 2000,
                    timerProgressBar: true,
                  });

                  // Reload replies after a delay
                  setTimeout(() => {
                    loadEmailReplies();
                  }, 2000);
                }
              } else {
                // Remove the overlay if it exists
                const overlay = replyElement?.querySelector(
                  ".email-sending-overlay"
                );
                if (overlay) overlay.remove();

                // Show error alert
                Swal.fire({
                  title: "Error!",
                  text: data.message || "Failed to send email",
                  icon: "error",
                  confirmButtonColor: "#d33",
                  confirmButtonText: "OK",
                });

                if (button) {
                  button.disabled = false;
                  button.innerHTML =
                    '<i class="bi bi-check-circle"></i> Accept & Send';
                }
              }
            })
            .catch((error) => {
              console.error("Error sending reply:", error);

              // Remove the overlay if it exists
              const overlay = replyElement?.querySelector(
                ".email-sending-overlay"
              );
              if (overlay) overlay.remove();

              // Show error alert
              Swal.fire({
                title: "Error!",
                text: "An error occurred while sending the email",
                icon: "error",
                confirmButtonColor: "#d33",
                confirmButtonText: "OK",
              });

              if (button) {
                button.disabled = false;
                button.innerHTML =
                  '<i class="bi bi-check-circle"></i> Accept & Send';
              }
            });
        }

        // Open the editor modal
        function openEditor(replyId) {
          currentReplyId = replyId;

          // Show loading state in the modal
          emailEditor.value = "Loading...";
          emailEditorModal.classList.add("show");

          // Get the reply content
          fetchWithRetry(`/api/email_replies/${replyId}`)
            .then((reply) => {
              if (reply.error) {
                throw new Error(reply.error);
              }

              // Set the editor content
              emailEditor.value = reply.modified_reply || reply.reply_body;
            })
            .catch((error) => {
              console.error("Error loading reply for editing:", error);

              // Show error in the editor
              emailEditor.value = `Error loading reply: ${
                error.message || "Unknown error"
              }\n\nPlease close this dialog and try again.`;

              // Show toast notification
              showToast("Error loading reply for editing", "danger");
            });
        }

        // Save edited reply with improved UX
        function saveEditedReply() {
          if (!currentReplyId) return;

          const modifiedReply = emailEditor.value.trim();
          if (!modifiedReply) {
            showToast("Reply cannot be empty", "warning");
            return;
          }

          // Show loading state
          saveEditBtn.disabled = true;
          saveEditBtn.innerHTML = '<span class="spinner"></span> Saving...';

          // Add a subtle overlay to the modal content
          const modalContent = document.querySelector(".editor-modal-content");
          if (modalContent) {
            modalContent.classList.add("saving");
          }

          fetchWithRetry(`/api/email_replies/${currentReplyId}/modify`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ modified_reply: modifiedReply }),
          })
            .then((data) => {
              if (data.status === "success") {
                // Close the modal
                closeEditorModal();

                // Show success toast
                showToast("Reply updated successfully", "success");

                // Reload replies
                loadEmailReplies();
              } else {
                // Remove saving class
                if (modalContent) modalContent.classList.remove("saving");

                // Show error toast
                showToast(data.error || "Failed to save changes", "danger");
              }

              saveEditBtn.disabled = false;
              saveEditBtn.innerHTML = "Save Changes";
            })
            .catch((error) => {
              console.error("Error saving edited reply:", error);

              // Remove saving class
              if (modalContent) modalContent.classList.remove("saving");

              // Show error toast
              showToast("An error occurred while saving changes", "danger");

              saveEditBtn.disabled = false;
              saveEditBtn.innerHTML = "Save Changes";
            });
        }

        // Close the editor modal
        function closeEditorModal() {
          emailEditorModal.classList.remove("show");
          currentReplyId = null;
          emailEditor.value = "";
        }

        // Open the regenerate modal
        function openRegenerateModal(replyId) {
          currentReplyId = replyId;

          // Show the modal with loading state
          regenerateModal.classList.add("show");

          // Set default values while loading
          toneSelect.value = "professional";
          lengthSelect.value = "medium";
          customInstructions.value = "Loading...";

          // Disable form controls while loading
          toneSelect.disabled = true;
          lengthSelect.disabled = true;
          customInstructions.disabled = true;
          confirmRegenerateBtn.disabled = true;

          // Get the reply details
          fetchWithRetry(`/api/email_replies/${replyId}`)
            .then((reply) => {
              if (reply.error) {
                throw new Error(reply.error);
              }

              // Set the form values
              toneSelect.value = reply.tone || "professional";
              lengthSelect.value = reply.length || "medium";
              customInstructions.value = reply.custom_instructions || "";

              // Re-enable form controls
              toneSelect.disabled = false;
              lengthSelect.disabled = false;
              customInstructions.disabled = false;
              confirmRegenerateBtn.disabled = false;
            })
            .catch((error) => {
              console.error("Error loading reply for regeneration:", error);

              // Show error in the custom instructions field
              customInstructions.value = `Error loading reply: ${
                error.message || "Unknown error"
              }\n\nPlease close this dialog and try again.`;

              // Show toast notification
              showToast("Error loading reply settings", "danger");

              // Keep form controls disabled
              toneSelect.disabled = true;
              lengthSelect.disabled = true;
              customInstructions.disabled = true;
              confirmRegenerateBtn.disabled = true;
            });
        }

        // Regenerate reply with improved UX
        function regenerateReply() {
          if (!currentReplyId) return;

          // Get form values
          const tone = toneSelect.value;
          const length = lengthSelect.value;
          const instructions = customInstructions.value.trim();

          // Show loading state
          regenerateSpinner.classList.remove("d-none");
          regenerateText.textContent = "Regenerating...";
          confirmRegenerateBtn.disabled = true;

          // Add a subtle overlay to the modal content
          const modalContent = document.querySelector(".editor-modal-content");
          if (modalContent) {
            modalContent.classList.add("regenerating");
          }

          fetchWithRetry(`/api/email_replies/${currentReplyId}/regenerate`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              tone: tone,
              length: length,
              custom_instructions: instructions || null,
            }),
          })
            .then((data) => {
              if (data.status === "success") {
                // Close the modal
                closeRegenerateModal();

                // Show success toast with tone and length info
                showToast(
                  `Reply regenerated with ${tone} tone and ${length} length`,
                  "success"
                );

                // Reload replies
                loadEmailReplies();
              } else {
                // Remove regenerating class
                if (modalContent) modalContent.classList.remove("regenerating");

                // Show error toast
                showToast(
                  data.message || "Failed to regenerate reply",
                  "danger"
                );
              }

              // Reset loading state
              regenerateSpinner.classList.add("d-none");
              regenerateText.textContent = "Regenerate Reply";
              confirmRegenerateBtn.disabled = false;
            })
            .catch((error) => {
              console.error("Error regenerating reply:", error);

              // Remove regenerating class
              if (modalContent) modalContent.classList.remove("regenerating");

              // Show error toast
              showToast(
                "An error occurred while regenerating the reply",
                "danger"
              );

              // Reset loading state
              regenerateSpinner.classList.add("d-none");
              regenerateText.textContent = "Regenerate Reply";
              confirmRegenerateBtn.disabled = false;
            });
        }

        // Close the regenerate modal
        function closeRegenerateModal() {
          regenerateModal.classList.remove("show");
          currentReplyId = null;
          toneSelect.value = "professional";
          lengthSelect.value = "medium";
          customInstructions.value = "";
        }

        // Discard all pending replies
        function discardAllReplies() {
          // Confirm with the user using SweetAlert2
          showDiscardAllConfirmation();
        }

        // Discard a reply with improved UX
        function discardReply(replyId) {
          const replyElement = document.querySelector(
            `.email-preview-container[data-reply-id="${replyId}"]`
          );

          // Add a discarding overlay
          if (replyElement) {
            const overlay = document.createElement("div");
            overlay.className = "email-sending-overlay";
            overlay.innerHTML = `
              <div class="email-sending-content">
                <div class="spinner-border text-secondary" role="status">
                  <span class="visually-hidden">Discarding...</span>
                </div>
                <h4 class="mt-3">Discarding reply...</h4>
              </div>
            `;
            replyElement.appendChild(overlay);
          }

          fetchWithRetry(`/api/email_replies/${replyId}/discard`, {
            method: "POST",
          })
            .then((data) => {
              if (data.status === "success") {
                // Remove the overlay
                const overlay = replyElement?.querySelector(
                  ".email-sending-overlay"
                );
                if (overlay) overlay.remove();

                // Show success animation
                if (replyElement) {
                  replyElement.innerHTML = `
                    <div class="email-discard-animation">
                      <div class="discard-icon">
                        <i class="bi bi-trash text-secondary" style="font-size: 3rem;"></i>
                      </div>
                      <h4 class="text-secondary text-center mt-3">Reply Discarded</h4>
                    </div>
                  `;

                  // Show success alert
                  Swal.fire({
                    title: "Discarded!",
                    text: "Reply has been discarded successfully",
                    icon: "success",
                    confirmButtonColor: "#2DAA9E",
                    confirmButtonText: "OK",
                    timer: 2000,
                    timerProgressBar: true,
                  });

                  // Reload replies after a delay
                  setTimeout(() => {
                    loadEmailReplies();
                  }, 2000);
                }
              } else {
                // Remove the overlay if it exists
                const overlay = replyElement?.querySelector(
                  ".email-sending-overlay"
                );
                if (overlay) overlay.remove();

                // Show error alert
                Swal.fire({
                  title: "Error!",
                  text: data.error || "Failed to discard reply",
                  icon: "error",
                  confirmButtonColor: "#d33",
                  confirmButtonText: "OK",
                });
              }
            })
            .catch((error) => {
              console.error("Error discarding reply:", error);

              // Remove the overlay if it exists
              const overlay = replyElement?.querySelector(
                ".email-sending-overlay"
              );
              if (overlay) overlay.remove();

              // Show error alert
              Swal.fire({
                title: "Error!",
                text: "An error occurred while discarding the reply",
                icon: "error",
                confirmButtonColor: "#d33",
                confirmButtonText: "OK",
              });
            });
        }

        // Helper function to format email body
        function formatEmailBody(body) {
          if (!body) return "";

          // Escape HTML
          const escaped = body
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");

          // Convert line breaks to <br>
          return escaped.replace(/\n/g, "<br>");
        }

        // Helper function to capitalize first letter
        function capitalizeFirstLetter(string) {
          return string.charAt(0).toUpperCase() + string.slice(1);
        }

        // Event listeners
        refreshRepliesBtn.addEventListener("click", loadEmailReplies);
        document
          .getElementById("discardAllBtn")
          .addEventListener("click", discardAllReplies);

        // Editor modal event listeners
        closeEditorBtn.addEventListener("click", closeEditorModal);
        cancelEditBtn.addEventListener("click", closeEditorModal);
        saveEditBtn.addEventListener("click", saveEditedReply);

        // Regenerate modal event listeners
        closeRegenerateBtn.addEventListener("click", closeRegenerateModal);
        cancelRegenerateBtn.addEventListener("click", closeRegenerateModal);
        confirmRegenerateBtn.addEventListener("click", regenerateReply);

        // Beautiful alert functions
        function showDiscardConfirmation(replyId) {
          Swal.fire({
            title: "Discard Reply?",
            text: "Are you sure you want to discard this reply?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#6c757d",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, discard it!",
            cancelButtonText: "Cancel",
            background: "#fff",
            backdrop: `rgba(0,0,0,0.4)`,
            borderRadius: "10px",
            customClass: {
              confirmButton: "btn btn-secondary",
              cancelButton: "btn btn-danger",
            },
          }).then((result) => {
            if (result.isConfirmed) {
              discardReply(replyId);
            }
          });
        }

        function showDiscardAllConfirmation() {
          Swal.fire({
            title: "Discard All Replies?",
            text: "Are you sure you want to discard ALL pending email replies? This action cannot be undone.",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#6c757d",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, discard all!",
            cancelButtonText: "Cancel",
            background: "#fff",
            backdrop: `rgba(0,0,0,0.4)`,
            borderRadius: "10px",
            customClass: {
              confirmButton: "btn btn-secondary",
              cancelButton: "btn btn-danger",
            },
          }).then((result) => {
            if (result.isConfirmed) {
              // Continue with discard all operation
              const discardAllBtn = document.getElementById("discardAllBtn");
              discardAllBtn.disabled = true;
              discardAllBtn.innerHTML =
                '<span class="spinner"></span> Discarding...';

              // Add overlay to the entire container
              const container = document.getElementById(
                "email-replies-container"
              );
              const overlay = document.createElement("div");
              overlay.className = "email-sending-overlay";
              overlay.style.borderRadius = "0";
              overlay.innerHTML = `
                <div class="email-sending-content">
                  <div class="spinner-border text-secondary" role="status">
                    <span class="visually-hidden">Discarding all replies...</span>
                  </div>
                  <h4 class="mt-3">Discarding all pending replies...</h4>
                </div>
              `;
              container.style.position = "relative";
              container.appendChild(overlay);

              // Call the API
              fetchWithRetry(`/api/email_replies/discard_all`, {
                method: "POST",
              })
                .then((data) => {
                  if (data.status === "success") {
                    // Show success toast
                    Swal.fire({
                      title: "Success!",
                      text: `Successfully discarded ${data.count} pending replies`,
                      icon: "success",
                      confirmButtonColor: "#2DAA9E",
                      confirmButtonText: "OK",
                      timer: 3000,
                      timerProgressBar: true,
                    });

                    // Reload replies
                    loadEmailReplies();
                  } else {
                    // Show error toast
                    Swal.fire({
                      title: "Error!",
                      text: data.error || "Failed to discard replies",
                      icon: "error",
                      confirmButtonColor: "#d33",
                      confirmButtonText: "OK",
                    });

                    // Remove overlay
                    if (overlay && overlay.parentNode) {
                      overlay.parentNode.removeChild(overlay);
                    }
                  }

                  // Reset button
                  discardAllBtn.disabled = false;
                  discardAllBtn.innerHTML =
                    '<i class="bi bi-trash"></i><span class="d-none d-sm-inline ms-1">Discard All</span>';
                })
                .catch((error) => {
                  console.error("Error discarding all replies:", error);

                  // Show error toast
                  Swal.fire({
                    title: "Error!",
                    text: "An error occurred while discarding replies",
                    icon: "error",
                    confirmButtonColor: "#d33",
                    confirmButtonText: "OK",
                  });

                  // Remove overlay
                  if (overlay && overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                  }

                  // Reset button
                  discardAllBtn.disabled = false;
                  discardAllBtn.innerHTML =
                    '<i class="bi bi-trash"></i><span class="d-none d-sm-inline ms-1">Discard All</span>';
                });
            }
          });
        }

        function showAcceptConfirmation(replyId) {
          Swal.fire({
            title: "Send Email Reply?",
            text: "Are you sure you want to send this email reply?",
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#2DAA9E",
            cancelButtonColor: "#6c757d",
            confirmButtonText: "Yes, send it!",
            cancelButtonText: "Cancel",
            background: "#fff",
            backdrop: `rgba(0,0,0,0.4)`,
            borderRadius: "10px",
            customClass: {
              confirmButton: "btn btn-success",
              cancelButton: "btn btn-secondary",
            },
          }).then((result) => {
            if (result.isConfirmed) {
              sendReply(replyId);
            }
          });
        }

        // Load email replies on page load
        loadEmailReplies();

        // Refresh email replies every 30 seconds
        setInterval(loadEmailReplies, 30000);
      });
    </script>
  </body>
</html>
