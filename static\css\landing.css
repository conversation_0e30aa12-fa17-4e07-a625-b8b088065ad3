/* Modern Landing Page CSS with Enhanced Colors, Fonts, and Animations */
:root {
  /* Enhanced Cohesive Color Palette */
  --primary: #077a7d; /* Teal - main brand color */
  --primary-dark: #06595b; /* Darker teal for contrast */
  --primary-light: #2daa9e; /* Lighter teal for accents */
  --secondary: #7ae2cf; /* Mint green - complementary to teal */
  --secondary-dark: #5ecfbc; /* Darker mint green */
  --accent: #f5eedd; /* Cream for backgrounds and accents */
  --accent-dark: #e3d2c3; /* Darker cream/beige */
  --light: #eaeaea; /* Light gray for backgrounds */
  --dark: #06202b; /* Dark navy for text - better contrast */
  --white: #ffffff;
  --gray: #495057; /* Darker gray for better readability */
  --gray-light: #e9ecef;
  --success: #2daa9e;
  --danger: #dc3545;
  --warning: #ffc107;
  --info: #077a7d;

  /* CTA Colors */
  --cta-bg: #077a7d; /* Teal for CTA */
  --cta-bg-hover: #06595b; /* Darker teal for hover */

  /* Title Gradient Colors */
  --title-gradient-1: #ff4d4d; /* Vibrant red */
  --title-gradient-2: #ff9900; /* Vibrant orange */
  --title-gradient-3: #7ae2cf; /* Mint green */
  --title-gradient-4: #077a7d; /* Teal */

  /* Gradient Colors */
  --gradient-hero: linear-gradient(135deg, #06202b 0%, #077a7d 100%);
  --gradient-cta: linear-gradient(135deg, #06202b 0%, #077a7d 100%);
  --gradient-features: linear-gradient(
    135deg,
    rgba(245, 238, 221, 0.9) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  --gradient-testimonials: linear-gradient(
    135deg,
    rgba(234, 234, 234, 0.3) 0%,
    rgba(227, 210, 195, 0.2) 100%
  );
  --gradient-card-hover: linear-gradient(
    135deg,
    rgba(122, 226, 207, 0.05) 0%,
    rgba(7, 122, 125, 0.1) 100%
  );
  --gradient-btn-primary: linear-gradient(135deg, #077a7d 0%, #06595b 100%);
  --gradient-btn-hover: linear-gradient(135deg, #06595b 0%, #077a7d 100%);
  --gradient-title: linear-gradient(
    to right,
    var(--title-gradient-1) 0%,
    var(--title-gradient-2) 30%,
    var(--title-gradient-3) 60%,
    var(--title-gradient-4) 100%
  );
  --gradient-animated-bg: linear-gradient(
    135deg,
    #06202b 0%,
    #077a7d 33%,
    #2daa9e 66%,
    #7ae2cf 100%
  );

  /* Typography */
  --font-primary: "Montserrat", sans-serif;
  --font-secondary: "Poppins", sans-serif;
  --font-body: "Roboto", sans-serif;
  --h1-size: clamp(2.75rem, 5.5vw, 4.5rem); /* Larger for more impact */
  --h2-size: clamp(2.25rem, 4.5vw, 3.5rem); /* Larger for section headers */
  --h3-size: clamp(1.5rem, 3vw, 2.25rem); /* Slightly larger */
  --body-size: clamp(
    1.05rem,
    1.5vw,
    1.2rem
  ); /* Larger for better readability */
  --small-size: clamp(0.9rem, 1vw, 1rem); /* Slightly larger */
  --btn-font-size: clamp(1.1rem, 1.5vw, 1.25rem); /* Larger button text */

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 2rem;
  --spacing-lg: 4rem;
  --spacing-xl: 8rem;

  /* Borders & Shadows */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 1rem;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  --box-shadow-hover: 0 15px 40px rgba(0, 0, 0, 0.12);
  --box-shadow-btn: 0 4px 10px rgba(45, 170, 158, 0.2);
  --box-shadow-btn-hover: 0 6px 15px rgba(45, 170, 158, 0.3);

  /* Transitions */
  --transition-fast: 0.3s ease;
  --transition-medium: 0.5s ease;
  --transition-slow: 0.8s ease;

  /* Animation Durations */
  --animation-short: 0.5s;
  --animation-medium: 0.8s;
  --animation-long: 1.2s;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  font-size: var(--body-size);
  line-height: 1.7; /* Increased line height for better readability */
  color: var(--dark);
  background-color: var(--white);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased; /* Smoother font rendering */
  -moz-osx-font-smoothing: grayscale;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-primary);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-sm);
  color: var(--dark); /* Ensure consistent color */
  letter-spacing: -0.02em; /* Slightly tighter letter spacing for headings */
}

p {
  margin-bottom: var(--spacing-md);
  color: var(--gray); /* Slightly lighter than headings for contrast */
  font-family: var(--font-body);
  font-weight: 400;
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: 500;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: none; /* Remove underline on hover as per user preference */
}

img {
  max-width: 100%;
  height: auto;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Enhanced Buttons with Better Visibility */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.9rem 1.8rem; /* Increased padding for better clickability */
  border-radius: var(--border-radius-md);
  font-family: var(--font-primary); /* Use primary font for buttons */
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  border: none;
  font-size: var(--btn-font-size); /* Larger font size for better visibility */
  gap: 0.5rem;
  letter-spacing: 0.02em; /* Slightly increased letter spacing */
  text-decoration: none !important; /* Ensure no underline on hover */
  min-width: 160px; /* Minimum width for better visibility */
  min-height: 50px; /* Minimum height for better touch targets */
}

.btn-primary {
  background: var(
    --gradient-btn-primary
  ); /* Use gradient for more visual appeal */
  color: var(--white);
  box-shadow: var(--box-shadow-btn);
  position: relative;
  overflow: hidden;
  z-index: 1;
  border: 2px solid var(--white); /* Add white border for contrast */
  transition: border-color 0.3s ease;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    /* Increased opacity for more visible effect */ transparent
  );
  transition: all 0.6s ease;
  z-index: -1;
}

.btn-primary:hover {
  background: var(--gradient-btn-hover); /* Reverse gradient on hover */
  transform: translateY(-5px) scale(1.03); /* Slightly larger scale effect */
  box-shadow: var(--box-shadow-btn-hover);
  color: var(--white);
  border-color: var(--secondary); /* Change border color on hover */
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:active {
  transform: translateY(-2px) scale(0.98); /* Pressed effect */
}

.btn-outline {
  background-color: transparent;
  color: var(--primary); /* Use primary color for better contrast */
  border: 2px solid var(--primary);
  position: relative;
  overflow: hidden;
  z-index: 1;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
}

.btn-outline::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: var(--secondary);
  transition: all 0.4s ease;
  z-index: -1;
}

.btn-outline:hover {
  color: var(--white);
  transform: translateY(-5px) scale(1.03);
  box-shadow: var(--box-shadow);
  border-color: var(--secondary);
  background-color: var(--primary); /* Fill with primary color on hover */
}

.btn-outline:hover::after {
  width: 100%;
}

.btn-outline:active {
  transform: translateY(-2px) scale(0.98); /* Pressed effect */
}

.btn-large {
  padding: 1.2rem 2.4rem; /* Even larger padding for main CTAs */
  font-size: calc(var(--btn-font-size) * 1.1); /* Slightly larger font */
  min-width: 200px; /* Wider for main CTAs */
}

/* Header & Navigation */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-fast);
}

.header.scrolled {
  padding: 0.5rem 0;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-dark);
  gap: 0.5rem;
}

.logo i {
  font-size: 1.75rem;
  color: var(--primary);
}

.nav-menu {
  display: flex;
  align-items: center;
  list-style: none;
  gap: 1.5rem;
}

.nav-link {
  color: var(--dark);
  font-family: var(--font-primary);
  font-weight: 600;
  font-size: 1.05rem;
  position: relative;
  padding: 0.5rem 0.75rem;
  transition: all var(--transition-fast);
  text-decoration: none !important;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 3px; /* Thicker underline */
  background-color: var(
    --secondary
  ); /* Use secondary color for better visibility */
  transition: width var(--transition-fast);
  border-radius: 3px;
}

.nav-link:hover {
  color: var(--secondary);
}

.nav-link:hover::after {
  width: 0; /* Remove underline effect on hover for nav links */
}

.nav-login {
  color: var(--primary);
  font-weight: 700;
  margin-left: 0.5rem;
}

.nav-login:hover {
  color: var(--primary-dark);
}

.nav-signup {
  background: var(--gradient-btn-primary);
  color: var(--white);
  padding: 0.6rem 1.2rem;
  border-radius: var(--border-radius-md);
  font-weight: 700;
  box-shadow: var(--box-shadow-btn);
  transition: all var(--transition-fast);
}

.nav-signup:hover {
  background: var(--gradient-btn-hover);
  color: var(--white);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-btn-hover);
}

.menu-toggle {
  display: none;
  cursor: pointer;
}

.bar {
  display: block;
  width: 25px;
  height: 3px;
  margin: 5px auto;
  background-color: var(--dark);
  transition: all var(--transition-fast);
}

/* Hero Section with Enhanced Background and Animations */
.hero {
  position: relative;
  padding: 10rem 0 6rem;
  overflow: hidden;
  background: var(--dark);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

/* Animated Background */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-animated-bg);
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
  z-index: 1;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(
    0,
    0,
    0,
    0.2
  ); /* Slightly lighter overlay for better visibility */
  z-index: 2;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.hero .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
  position: relative;
  z-index: 3;
}

.hero-content {
  flex: 1;
  max-width: 600px;
}

.hero-content.centered {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(
    to right,
    #ff4d4d,
    #ff9900,
    #7ae2cf,
    #077a7d,
    #ff4d4d
  );
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  font-size: calc(var(--h1-size) * 0.9); /* Reduced size */
  margin-bottom: var(--spacing-md);
  line-height: 1.1;
  opacity: 1;
  animation: gradientShift 6s linear infinite;
  font-weight: 700; /* Reduced from 800 */
  letter-spacing: -0.02em; /* Reduced letter spacing */
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Reduced shadow */
  text-align: center;
  position: relative;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.gradient-text.cta-title {
  font-size: calc(var(--h1-size) * 1.1); /* Reduced from 1.5 to 1.1 */
  font-weight: 800; /* Slightly reduced from 900 */
  width: 100%;
  max-width: 800px; /* Reduced from 900px */
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  text-transform: none;
  letter-spacing: -0.01em; /* Reduced letter spacing */
  line-height: 1.2;
  padding: 10px 0; /* Reduced padding */
  text-shadow: 0 2px 5px rgba(255, 255, 255, 0.1); /* Reduced shadow */
  transform: scale(1);
  transition: transform 0.3s ease;
}

.gradient-text.cta-title:hover {
  transform: scale(1.02);
}

/* Enhanced Hero Title */
.hero-title {
  position: relative;
  z-index: 10;
  margin-bottom: 1.5rem;
}

/* Enhanced Professional Carousel Styles */
.carousel-container {
  width: 100%;
  max-width: 1000px;
  margin: 3rem auto 0;
  position: relative;
  transition: all var(--transition-medium);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Device Frame Styling */
.carousel-device-frame {
  width: 100%;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  transition: all 0.4s ease;
  transform-origin: center bottom;
}

.carousel-device-frame:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.05);
}

.carousel-device-header {
  background: linear-gradient(to right, var(--primary-dark), var(--primary));
  padding: 12px 15px;
  display: flex;
  align-items: center;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.carousel-device-buttons {
  display: flex;
  gap: 6px;
  margin-right: 15px;
}

.carousel-device-buttons span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: block;
}

.carousel-device-buttons span:nth-child(1) {
  background-color: #ff5f57;
}

.carousel-device-buttons span:nth-child(2) {
  background-color: #ffbd2e;
}

.carousel-device-buttons span:nth-child(3) {
  background-color: #28ca41;
}

.carousel-device-title {
  color: var(--white);
  font-size: 14px;
  font-weight: 600;
  flex-grow: 1;
  text-align: center;
}

/* Slides Styling */
.carousel-slides {
  position: relative;
  width: 100%;
  background-color: #f5f5f7;
  overflow: hidden;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.carousel-slide.active {
  opacity: 1;
  visibility: visible;
  position: relative;
}

.carousel-image {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
}

.carousel-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 20px;
  color: var(--white);
  text-align: left;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.carousel-slide.active .carousel-caption {
  opacity: 1;
  transform: translateY(0);
}

.carousel-caption h4 {
  margin: 0 0 5px;
  font-size: 18px;
  font-weight: 700;
}

.carousel-caption p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* Navigation Styling */
.carousel-navigation {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.carousel-thumbnails {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.carousel-thumbnail {
  width: 120px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  opacity: 0.7;
  transform: scale(0.95);
}

.carousel-thumbnail:hover {
  opacity: 0.9;
  transform: scale(1);
}

.carousel-thumbnail.active {
  opacity: 1;
  transform: scale(1);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15), 0 0 0 2px var(--primary);
}

.carousel-thumbnail img {
  width: 100%;
  height: 70px;
  object-fit: cover;
  display: block;
}

.carousel-thumbnail span {
  display: block;
  padding: 6px 10px;
  background-color: var(--white);
  color: var(--dark);
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Controls Styling */
.carousel-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
}

.carousel-prev,
.carousel-next {
  background-color: var(--white);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--primary);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.carousel-prev:hover,
.carousel-next:hover {
  background-color: var(--primary);
  color: var(--white);
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.carousel-indicators {
  display: flex;
  gap: 8px;
}

.carousel-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--light);
  border: 1px solid var(--primary-light);
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-indicator.active {
  background-color: var(--primary);
  transform: scale(1.2);
}

/* Application Preview */
.app-preview {
  margin-bottom: var(--spacing-md);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-medium) ease forwards;
  animation-delay: 0.2s;
  max-width: 80%;
  margin-left: auto;
  margin-right: auto;
}

.preview-text {
  font-size: 1.35rem;
  color: var(--white);
  font-weight: 400;
  line-height: 1.6;
  text-align: center;
}

/* Enhanced CTA Button */
.hero-cta {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-medium) ease forwards;
  animation-delay: 0.4s;
}

.btn-cta {
  background: var(--cta-bg);
  color: var(--white);
  padding: 1.2rem 2.4rem;
  font-size: 1.3rem;
  font-weight: 700;
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 15px rgba(255, 87, 51, 0.4);
  transition: all 0.3s ease;
  min-width: 250px;
  text-align: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-cta:hover {
  background: var(--cta-bg-hover);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 87, 51, 0.5);
  color: var(--white);
}

.btn-cta:active {
  transform: translateY(-2px) scale(0.98);
}

.hero-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-medium) ease forwards;
  animation-delay: 0.6s;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.feature-item i {
  color: var(--white);
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  opacity: 0;
  transform: translateX(20px);
  animation: fadeInRight var(--animation-medium) ease forwards,
    float 6s ease-in-out infinite 1s;
  animation-delay: 0.8s;
}

.hero-preview-image {
  margin-top: var(--spacing-lg);
  max-width: 800px;
  width: 100%;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-medium) ease forwards,
    float 6s ease-in-out infinite 2s;
  animation-delay: 0.8s;
}

/* Features Section with Enhanced Background and Animations */
.features {
  padding: var(--spacing-xl) 0;
  background: var(--gradient-features);
  position: relative;
  overflow: hidden;
}

.features::before {
  content: "";
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: var(--gradient-card-hover);
  opacity: 0.5;
  z-index: 0;
}

.features::after {
  content: "";
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: var(--gradient-card-hover);
  opacity: 0.3;
  z-index: 0;
}

.section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--spacing-lg);
  position: relative;
  z-index: 1;
}

.section-tag {
  display: block; /* Changed from inline-block to block to ensure it appears on its own line */
  background-color: rgba(
    7,
    122,
    125,
    0.15
  ); /* Updated to match our new color scheme */
  color: var(--primary-dark);
  padding: 0.3rem 1.2rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 700;
  font-family: var(--font-primary);
  margin-bottom: var(--spacing-sm);
  letter-spacing: 1.5px;
  text-transform: uppercase;
  box-shadow: 0 2px 10px rgba(7, 122, 125, 0.2);
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp var(--animation-medium) ease forwards;
  margin-left: auto;
  margin-right: auto;
  max-width: fit-content; /* Ensure it's only as wide as needed */
}

.section-title {
  font-size: var(--h2-size);
  margin-bottom: var(--spacing-md);
  color: var(--dark);
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp var(--animation-medium) ease forwards;
  animation-delay: 0.2s;
  font-weight: 800; /* Extra bold for more impact */
  letter-spacing: -0.02em; /* Slightly tighter letter spacing */
  position: relative;
  display: block; /* Changed from inline-block to block */
  width: 100%; /* Ensure it takes full width */
  text-align: center; /* Center the text */
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: var(--gradient-btn-primary); /* Using our teal gradient */
  border-radius: 2px;
}

.section-subtitle {
  color: var(--gray);
  font-size: 1.25rem; /* Larger for better readability */
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp var(--animation-medium) ease forwards;
  animation-delay: 0.3s;
  max-width: 700px; /* Prevent overly long lines */
  margin: 1.5rem auto 0; /* Add more space after the title underline */
  font-weight: 400;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-md);
  position: relative;
  z-index: 1;
}

.feature-card {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg); /* Increased padding for more whitespace */
  box-shadow: var(--box-shadow);
  transition: all var(--transition-medium);
  height: 100%;
  display: flex;
  flex-direction: column;
  transform: translateY(30px);
  opacity: 0;
  animation: fadeInUp var(--animation-medium) ease forwards;
  border: 1px solid var(--accent-dark); /* Enhanced border with accent color */
  overflow: hidden; /* For the background gradient effect */
  position: relative;
}

.feature-card:nth-child(odd):hover {
  animation: borderPulse 2s infinite;
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-card-hover);
  opacity: 0;
  transition: opacity var(--transition-medium);
  z-index: 0;
}

.feature-card:nth-child(1) {
  animation-delay: 0.3s;
}

.feature-card:nth-child(2) {
  animation-delay: 0.4s;
}

.feature-card:nth-child(3) {
  animation-delay: 0.5s;
}

.feature-card:nth-child(4) {
  animation-delay: 0.6s;
}

.feature-card:nth-child(5) {
  animation-delay: 0.7s;
}

.feature-card:nth-child(6) {
  animation-delay: 0.8s;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--box-shadow-hover);
  border-color: var(--primary); /* Change border color to primary on hover */
  border-width: 2px; /* Slightly thicker border on hover */
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  width: 70px; /* Larger icon */
  height: 70px; /* Larger icon */
  background-color: rgba(
    7,
    122,
    125,
    0.1
  ); /* Updated to match our teal color scheme */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-medium);
  position: relative;
  z-index: 1;
  box-shadow: 0 5px 15px rgba(7, 122, 125, 0.15);
}

.feature-card:hover .feature-icon {
  background-color: var(--primary);
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 20px rgba(7, 122, 125, 0.25);
}

.feature-icon i {
  font-size: 2rem; /* Larger icon */
  color: var(--primary);
  transition: all var(--transition-medium);
}

.feature-card:hover .feature-icon i {
  color: var(--white);
}

.feature-card h3 {
  font-size: 1.6rem; /* Larger heading */
  margin-bottom: var(--spacing-sm);
  color: var(--dark);
  transition: all var(--transition-medium);
  font-weight: 700;
  position: relative;
  z-index: 1;
  font-family: var(--font-primary);
}

.feature-card:hover h3 {
  color: var(--primary-dark);
}

.feature-card p {
  color: var(--gray);
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-medium);
  position: relative;
  z-index: 1;
  font-size: 1.05rem; /* Slightly larger text */
  line-height: 1.7;
  font-weight: 400;
}

/* How It Works Section with Enhanced Background and Animations */
.how-it-works {
  padding: var(--spacing-xl) 0;
  background: linear-gradient(135deg, var(--light) 0%, var(--gray-light) 100%);
  position: relative;
  overflow: hidden;
}

.how-it-works::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-image: radial-gradient(
      circle at 10% 20%,
      rgba(102, 210, 206, 0.1) 0%,
      transparent 20%
    ),
    radial-gradient(
      circle at 90% 80%,
      rgba(102, 210, 206, 0.1) 0%,
      transparent 20%
    );
  z-index: 0;
}

.how-it-works .section-header {
  position: relative;
  z-index: 1;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.step {
  display: flex;
  gap: var(--spacing-md);
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  box-shadow: var(--box-shadow);
  transition: all var(--transition-medium);
  opacity: 0;
  transform: translateX(-30px);
  animation: fadeInLeft var(--animation-medium) ease forwards;
  border: 1px solid var(--accent-dark); /* Add subtle border */
  overflow: hidden; /* For animation effects */
}

.step:nth-child(even) {
  transform: translateX(30px);
  animation: fadeInRight var(--animation-medium) ease forwards;
}

.step:nth-child(1) {
  animation-delay: 0.3s;
}

.step:nth-child(2) {
  animation-delay: 0.5s;
}

.step:nth-child(3) {
  animation-delay: 0.7s;
}

.step:nth-child(4) {
  animation-delay: 0.9s;
}

.step:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-hover);
  background: var(--gradient-card-hover);
  border-color: var(--primary); /* Change border color on hover */
  border-width: 2px; /* Slightly thicker border on hover */
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  );
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  font-weight: 700;
  flex-shrink: 0;
  box-shadow: 0 4px 10px rgba(7, 122, 125, 0.3);
  transition: all var(--transition-medium);
  border: 2px solid var(--white); /* Add white border for contrast */
}

.step:hover .step-number {
  transform: scale(1.1) rotate(10deg);
}

.step-content {
  flex: 1;
}

.step-content h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--dark);
  transition: all var(--transition-medium);
}

.step:hover .step-content h3 {
  color: var(--primary-dark);
}

.step-content p {
  color: var(--gray);
  margin-bottom: 0;
  transition: all var(--transition-medium);
}

/* Testimonials Section with Enhanced Background and Animations */
.testimonials {
  padding: var(--spacing-xl) 0;
  background: var(--gradient-testimonials);
  position: relative;
  overflow: hidden;
}

.testimonials::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 80% 20%,
      rgba(102, 210, 206, 0.1) 0%,
      transparent 25%
    ),
    radial-gradient(
      circle at 20% 80%,
      rgba(227, 210, 195, 0.2) 0%,
      transparent 25%
    );
  z-index: 0;
}

.testimonials .section-header {
  position: relative;
  z-index: 1;
}

.testimonials-slider {
  display: flex;
  gap: var(--spacing-md);
  overflow-x: auto;
  padding: var(--spacing-sm) 0;
  scroll-snap-type: x mandatory;
  scrollbar-width: none; /* Firefox */
  position: relative;
  z-index: 1;
}

.testimonials-slider::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.testimonial-card {
  flex: 0 0 calc(33.333% - var(--spacing-md));
  scroll-snap-align: start;
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  box-shadow: var(--box-shadow);
  transition: all var(--transition-medium);
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateX(30px);
  animation: fadeInRight var(--animation-medium) ease forwards;
  border: 1px solid var(--accent-dark); /* Add subtle border */
  overflow: hidden; /* For animation effects */
}

.testimonial-card:nth-child(1) {
  animation-delay: 0.3s;
}

.testimonial-card:nth-child(2) {
  animation-delay: 0.5s;
}

.testimonial-card:nth-child(3) {
  animation-delay: 0.7s;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-hover);
  background: var(--gradient-card-hover);
  border-color: var(--primary); /* Change border color on hover */
  border-width: 2px; /* Slightly thicker border on hover */
}

.testimonial-content {
  flex: 1;
  margin-bottom: var(--spacing-md);
  position: relative;
}

.testimonial-content::before {
  content: '"';
  position: absolute;
  top: -20px;
  left: -10px;
  font-size: 4rem;
  color: rgba(102, 210, 206, 0.2);
  font-family: Georgia, serif;
  line-height: 1;
}

.testimonial-content p {
  font-style: italic;
  color: var(--dark);
  margin-bottom: 0;
  position: relative;
  z-index: 1;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.testimonial-author img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-light);
  transition: all var(--transition-medium);
}

.testimonial-card:hover .testimonial-author img {
  transform: scale(1.1);
  border-color: var(--primary);
}

.author-info h4 {
  font-size: 1rem;
  margin-bottom: 0;
  transition: all var(--transition-medium);
}

.testimonial-card:hover .author-info h4 {
  color: var(--primary-dark);
}

.author-info p {
  font-size: var(--small-size);
  color: var(--gray);
  margin-bottom: 0;
}

.testimonial-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: var(--spacing-md);
  position: relative;
  z-index: 1;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--light);
  cursor: pointer;
  transition: all var(--transition-medium);
  opacity: 0;
  transform: scale(0);
  animation: scaleIn var(--animation-short) ease forwards;
}

.dot:nth-child(1) {
  animation-delay: 0.9s;
}

.dot:nth-child(2) {
  animation-delay: 1s;
}

.dot:nth-child(3) {
  animation-delay: 1.1s;
}

.dot:hover {
  transform: scale(1.2);
}

.dot.active {
  background-color: var(--primary);
  transform: scale(1.2);
}

/* Pricing Section with Enhanced Background and Animations */
.pricing {
  padding: var(--spacing-xl) 0;
  background: linear-gradient(135deg, var(--light) 0%, var(--white) 100%);
  position: relative;
  overflow: hidden;
}

.pricing::before {
  content: "";
  position: absolute;
  top: -100px;
  right: -100px;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(102, 210, 206, 0.1) 0%,
    transparent 70%
  );
  z-index: 0;
}

.pricing::after {
  content: "";
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(227, 210, 195, 0.2) 0%,
    transparent 70%
  );
  z-index: 0;
}

.pricing .section-header {
  position: relative;
  z-index: 1;
}

.pricing-cards {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.pricing-card {
  flex: 1;
  min-width: 280px;
  max-width: 350px;
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: all var(--transition-medium);
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp var(--animation-medium) ease forwards;
  border: 1px solid var(--accent-dark); /* Add subtle border */
}

.pricing-card:nth-child(1) {
  animation-delay: 0.3s;
}

.pricing-card:nth-child(2) {
  animation-delay: 0.5s;
}

.pricing-card:nth-child(3) {
  animation-delay: 0.7s;
}

.pricing-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--box-shadow-hover);
  border-color: var(--primary); /* Change border color on hover */
  border-width: 2px; /* Slightly thicker border on hover */
}

.pricing-card.featured {
  transform: scale(1.05);
  border: 3px solid var(--primary);
  z-index: 1;
  animation-delay: 0.4s;
  box-shadow: 0 10px 30px rgba(7, 122, 125, 0.15); /* Enhanced shadow */
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
  position: absolute;
  top: 0;
  right: var(--spacing-md);
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  );
  color: var(--white);
  padding: 0.5rem 1.5rem;
  border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
  font-size: var(--small-size);
  font-weight: 700;
  box-shadow: 0 4px 8px rgba(7, 122, 125, 0.3);
  border-left: 2px solid var(--white);
  border-right: 2px solid var(--white);
  border-bottom: 2px solid var(--white);
}

.pricing-header {
  padding: var(--spacing-md);
  text-align: center;
  border-bottom: 1px solid var(--light);
  background: linear-gradient(
    180deg,
    rgba(245, 245, 247, 0.5) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

.pricing-header h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--dark);
  transition: all var(--transition-medium);
}

.pricing-card:hover .pricing-header h3 {
  color: var(--primary-dark);
}

.pricing-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
  transition: all var(--transition-medium);
}

.pricing-card:hover .pricing-price {
  transform: scale(1.05);
}

.currency {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dark);
}

.amount {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-dark);
  transition: all var(--transition-medium);
}

.pricing-card:hover .amount {
  color: var(--primary);
}

.period {
  font-size: 1rem;
  color: var(--gray);
}

.pricing-features {
  padding: var(--spacing-md);
}

.pricing-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pricing-features li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: var(--gray);
  transition: all var(--transition-medium);
  transform: translateX(0);
}

.pricing-card:hover .pricing-features li {
  transform: translateX(5px);
}

.pricing-features li:nth-child(1) {
  transition-delay: 0.05s;
}

.pricing-features li:nth-child(2) {
  transition-delay: 0.1s;
}

.pricing-features li:nth-child(3) {
  transition-delay: 0.15s;
}

.pricing-features li:nth-child(4) {
  transition-delay: 0.2s;
}

.pricing-features li:nth-child(5) {
  transition-delay: 0.25s;
}

.pricing-features li:nth-child(6) {
  transition-delay: 0.3s;
}

.pricing-features i {
  color: var(--primary);
  font-size: 1.25rem;
  transition: all var(--transition-medium);
}

.pricing-card:hover .pricing-features i {
  transform: scale(1.2);
}

.pricing-features i.bi-x-lg {
  color: var(--gray);
}

.pricing-cta {
  padding: 0 var(--spacing-md) var(--spacing-md);
  text-align: center;
}

/* FAQ Section with Enhanced Background and Animations */
.faq {
  padding: var(--spacing-xl) 0;
  background: linear-gradient(135deg, var(--white) 0%, var(--light) 100%);
  position: relative;
  overflow: hidden;
}

.faq::before {
  content: "";
  position: absolute;
  top: -50px;
  left: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(102, 210, 206, 0.1) 0%,
    transparent 70%
  );
  z-index: 0;
}

.faq::after {
  content: "";
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(227, 210, 195, 0.2) 0%,
    transparent 70%
  );
  z-index: 0;
}

.faq .section-header {
  position: relative;
  z-index: 1;
}

.faq-accordion {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.faq-item {
  margin-bottom: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  background-color: var(--white);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-medium) ease forwards;
  border: 1px solid var(--accent-dark); /* Add subtle border */
  transition: all var(--transition-medium);
}

.faq-item:nth-child(1) {
  animation-delay: 0.3s;
}

.faq-item:nth-child(2) {
  animation-delay: 0.4s;
}

.faq-item:nth-child(3) {
  animation-delay: 0.5s;
}

.faq-item:nth-child(4) {
  animation-delay: 0.6s;
}

.faq-item:nth-child(5) {
  animation-delay: 0.7s;
}

.faq-question {
  padding: var(--spacing-md);
  background-color: var(--white);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all var(--transition-medium);
}

.faq-item:hover {
  border-color: var(--primary);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-hover);
}

.faq-question:hover {
  background-color: rgba(7, 122, 125, 0.05);
}

.faq-question h3 {
  font-size: 1.125rem;
  margin-bottom: 0;
  color: var(--dark);
  transition: all var(--transition-medium);
}

.faq-item:hover .faq-question h3 {
  color: var(--primary-dark);
}

.faq-question i {
  font-size: 1.25rem;
  color: var(--primary);
  transition: all var(--transition-medium);
}

.faq-item.active .faq-question i {
  transform: rotate(45deg);
}

.faq-answer {
  padding: 0 var(--spacing-md) var(--spacing-md);
  display: none;
  animation: fadeIn var(--animation-short) ease;
}

.faq-item.active .faq-answer {
  display: block;
}

/* CTA Section with Enhanced Background and Animations (Simplified) */
.cta {
  padding: var(--spacing-lg) 0;
  position: relative;
  overflow: hidden;
  min-height: 40vh; /* Reduced height for simpler design */
  display: flex;
  align-items: center;
  color: var(--white);
  box-shadow: 0 0 30px rgba(7, 122, 125, 0.3); /* Add glow effect with primary color */
  border-top: 3px solid var(--secondary); /* Add border at top */
  border-bottom: 3px solid var(--secondary); /* Add border at bottom */
}

.cta .animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-animated-bg);
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
  z-index: 1;
}

.cta .gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.cta-content {
  text-align: center;
  max-width: 700px; /* Reduced width for simpler design */
  margin: 0 auto;
  position: relative;
  z-index: 3;
  padding: 3rem 1rem; /* Increased vertical padding */
}

.cta-content.centered {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* Better vertical centering */
}

.cta-content h2 {
  font-size: calc(var(--h2-size) * 1.1);
  margin-bottom: var(--spacing-md);
  color: var(--white);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-medium) ease forwards;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  font-weight: 800;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.cta-content .gradient-text {
  font-size: calc(var(--h2-size) * 1.1);
  margin-bottom: var(--spacing-md);
  background: var(--gradient-title);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: none;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-medium) ease forwards;
}

.cta-content .cta-title {
  font-size: calc(var(--h2-size) * 1.2); /* Reduced from 1.5 to 1.2 */
  font-weight: 800;
  text-align: center;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
}

.cta-content .app-preview {
  margin-bottom: var(--spacing-lg);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-medium) ease forwards;
  animation-delay: 0.2s;
  max-width: 80%;
  margin-left: auto;
  margin-right: auto;
}

.cta-content .app-preview p {
  color: var(--white);
  font-size: 1.1rem; /* Reduced from 1.35rem to 1.1rem */
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  margin-bottom: 0;
}

.cta .btn-cta {
  background: var(--cta-bg);
  color: var(--white);
  padding: 1.2rem 2.8rem; /* Reduced padding */
  font-size: 1.2rem; /* Reduced font size */
  font-weight: 700;
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 15px rgba(7, 122, 125, 0.4);
  transition: all 0.3s ease;
  min-width: 250px; /* Slightly narrower button */
  text-align: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-medium) ease forwards;
  animation-delay: 0.4s;
  border: 2px solid var(--white); /* Thinner border */
  margin-top: 1.5rem; /* Add more space above the button */
  display: inline-block; /* Ensure it's displayed as a block */
  text-decoration: none !important; /* Ensure no text decoration */
  visibility: visible !important; /* Ensure it's always visible */
}

.cta .btn-cta:hover {
  background: var(--cta-bg-hover);
  transform: translateY(-3px); /* Minimal transform to prevent disappearing */
  box-shadow: 0 6px 15px rgba(7, 122, 125, 0.4); /* Subtle shadow */
  color: var(--white);
  border-color: var(--secondary); /* Change border color on hover */
  opacity: 1 !important; /* Force opacity to be 1 */
  visibility: visible !important; /* Force visibility */
  text-decoration: none !important; /* Ensure no text decoration */
}

/* CTA image styles removed as they're no longer needed */

/* Footer with Enhanced Background */
.footer {
  padding: var(--spacing-lg) 0 var(--spacing-md);
  background: linear-gradient(135deg, var(--accent) 0%, var(--white) 100%);
  position: relative;
  overflow: hidden;
  border-top: 3px solid var(--primary-light); /* Add border at top */
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 90% 10%,
      rgba(102, 210, 206, 0.05) 0%,
      transparent 30%
    ),
    radial-gradient(
      circle at 10% 90%,
      rgba(227, 210, 195, 0.1) 0%,
      transparent 30%
    );
  z-index: 0;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  position: relative;
  z-index: 1;
}

.footer-brand {
  margin-bottom: var(--spacing-md);
}

.footer-logo {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: var(--spacing-sm);
  gap: 0.5rem;
}

.footer-logo i {
  font-size: 1.75rem;
  color: var(--primary);
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: var(--spacing-md);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(7, 122, 125, 0.1);
  color: var(--primary-dark);
  transition: all var(--transition-fast);
  border: 1px solid var(--primary-light); /* Add subtle border */
}

.social-links a:hover {
  background-color: var(--primary);
  color: var(--white);
  transform: translateY(-3px) scale(1.1);
  border-color: var(--white); /* Change border color on hover */
  box-shadow: 0 5px 15px rgba(7, 122, 125, 0.3); /* Add subtle shadow */
}

.footer-links h4 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-md);
  color: var(--dark);
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: var(--gray);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--primary-dark);
}

.footer-newsletter h4 {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-sm);
  color: var(--dark);
}

.newsletter-form {
  display: flex;
  gap: 0.5rem;
}

.newsletter-form input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid var(--primary-light);
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 5px rgba(7, 122, 125, 0.1);
}

.newsletter-form input:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 4px 10px rgba(7, 122, 125, 0.2);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--primary-light);
  margin-top: var(--spacing-md);
}

.footer-bottom p {
  margin-bottom: 0;
  color: var(--gray);
}

.footer-legal {
  display: flex;
  gap: 1.5rem;
}

.footer-legal a {
  color: var(--gray);
  font-size: var(--small-size);
}

/* Enhanced Cohesive Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: translateY(-20px);
    box-shadow: 0 25px 40px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: translateY(0px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    border-color: var(--white);
  }
  50% {
    transform: scale(1.05);
    border-color: var(--secondary);
  }
  100% {
    transform: scale(1);
    border-color: var(--white);
  }
}

@keyframes borderPulse {
  0% {
    border-color: var(--primary-light);
  }
  50% {
    border-color: var(--primary);
  }
  100% {
    border-color: var(--primary-light);
  }
}

/* Border Animation Class */
.border-animated {
  animation: borderPulse 4s infinite;
}

/* Fixed Button Class to prevent disappearing */
.btn-fixed {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 100 !important;
}

.btn-fixed:hover {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  text-decoration: none !important;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .hero .container {
    flex-direction: column;
    text-align: center;
  }

  .hero-content {
    max-width: 100%;
  }

  .hero-cta {
    justify-content: center;
  }

  .hero-features {
    align-items: center;
  }

  .testimonial-card {
    flex: 0 0 calc(50% - var(--spacing-md));
  }

  .gradient-text {
    font-size: calc(var(--h1-size) * 0.8);
  }

  .app-preview {
    max-width: 90%;
  }

  .preview-text {
    font-size: 1.2rem;
  }

  .cta-content .cta-title {
    font-size: calc(var(--h2-size) * 1.1);
    max-width: 100%;
  }

  .cta .btn-cta {
    min-width: 220px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .menu-toggle {
    display: block;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: var(--white);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-item {
    margin: 1.5rem 0;
  }

  .pricing-cards {
    flex-direction: column;
    align-items: center;
  }

  .pricing-card {
    width: 100%;
    max-width: 400px;
    margin-bottom: var(--spacing-md);
  }

  .pricing-card.featured {
    transform: scale(1);
  }

  .pricing-card.featured:hover {
    transform: translateY(-10px);
  }

  .testimonial-card {
    flex: 0 0 calc(100% - var(--spacing-sm));
  }

  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .footer-legal {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  /* Mobile Carousel Styles */
  .carousel-container {
    gap: 0.5rem;
  }

  .carousel-device-buttons {
    gap: 4px;
  }

  .carousel-device-buttons span {
    width: 8px;
    height: 8px;
  }

  .carousel-device-title {
    font-size: 12px;
  }

  .carousel-caption {
    padding: 10px;
  }

  .carousel-caption h4 {
    font-size: 14px;
  }

  .carousel-caption p {
    font-size: 11px;
  }

  .carousel-thumbnails {
    gap: 6px;
  }

  .carousel-thumbnail {
    width: 70px;
  }

  .carousel-thumbnail img {
    height: 45px;
  }

  .carousel-thumbnail span {
    padding: 4px 6px;
    font-size: 10px;
  }

  .carousel-prev,
  .carousel-next {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }

  .carousel-indicator {
    width: 8px;
    height: 8px;
  }
  .hero-cta {
    flex-direction: column;
    width: 100%;
  }

  .hero-cta .btn {
    width: 100%;
  }

  .cta .btn-cta {
    min-width: 100%;
    padding: 0.9rem 1.5rem;
    font-size: 1rem;
    display: block;
  }

  .step {
    flex-direction: column;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .newsletter-form input,
  .newsletter-form button {
    width: 100%;
  }

  .gradient-text {
    font-size: calc(var(--h1-size) * 0.6);
  }

  .gradient-text.cta-title {
    font-size: calc(var(--h1-size) * 0.8);
  }

  .app-preview {
    max-width: 100%;
  }

  .preview-text {
    font-size: 1.1rem;
  }

  .hero {
    min-height: auto;
    padding: 8rem 0 4rem;
  }

  .cta {
    min-height: auto;
    padding: var(--spacing-lg) 0;
  }

  .cta-content .cta-title {
    font-size: calc(var(--h2-size) * 0.9);
  }

  .cta-content .app-preview p {
    font-size: 0.95rem;
    line-height: 1.4;
  }

  .cta .btn-cta {
    min-width: 100%;
    padding: 1rem 1.5rem;
    font-size: 1.2rem;
  }

  .carousel-container {
    max-width: 100%;
    margin-top: var(--spacing-md);
    gap: 1rem;
  }

  .carousel-device-header {
    padding: 10px;
  }

  .carousel-device-buttons span {
    width: 10px;
    height: 10px;
  }

  .carousel-caption {
    padding: 15px;
  }

  .carousel-caption h4 {
    font-size: 16px;
  }

  .carousel-caption p {
    font-size: 12px;
  }

  .carousel-thumbnails {
    gap: 10px;
  }

  .carousel-thumbnail {
    width: 100px;
  }

  .carousel-thumbnail img {
    height: 60px;
  }

  .carousel-prev,
  .carousel-next {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
}
