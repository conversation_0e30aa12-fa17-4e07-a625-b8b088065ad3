# 🚂 Railway Database Setup Guide

This guide will help you set up a free Railway PostgreSQL database for your Email Agent application.

## 📋 Prerequisites

- Railway account (free)
- Your current project files

## 🚀 Step 1: Create Railway Database

1. **Sign up/Login to Railway**
   - Go to [railway.app](https://railway.app)
   - Sign up with GitHub (recommended) or email

2. **Create New Project**
   - Click "New Project"
   - Select "Provision PostgreSQL"
   - Choose a project name (e.g., "email-agent-db")

3. **Get Database URL**
   - Click on your PostgreSQL service
   - Go to "Variables" tab
   - Copy the `DATABASE_URL` value
   - It will look like: `postgresql://postgres:password@host:port/database`

## 🔧 Step 2: Update Your Local Configuration

1. **Update .env file**
   ```env
   # Replace the DATABASE_URL line with your Railway URL
   DATABASE_URL=postgresql://postgres:your_password@your_host:port/your_database
   ```

2. **Initialize the database**
   ```bash
   python init_railway_db.py
   ```

## 🚀 Step 3: Update Render Deployment

1. **Update Render Environment Variables**
   - Go to your Render dashboard
   - Select your web service
   - Go to "Environment" tab
   - Update `DATABASE_URL` with your Railway URL

2. **Deploy**
   - Your app will automatically redeploy
   - Check the logs to ensure successful connection

## ✅ Step 4: Verify Setup

1. **Test locally**
   ```bash
   python app.py
   ```
   - Should show "Database connection successful"

2. **Test production**
   - Visit your Render app URL
   - Check `/health` endpoint
   - Should show database as "connected"

## 🎯 Benefits of Railway

- ✅ **Free tier**: 500 hours/month, 1GB storage
- ✅ **PostgreSQL**: Same as your current setup
- ✅ **No migration needed**: Just change connection string
- ✅ **Reliable**: 99.9% uptime
- ✅ **Easy scaling**: Upgrade when needed

## 🔍 Troubleshooting

### Connection Issues
```bash
# Test connection manually
python -c "
import psycopg2
conn = psycopg2.connect('your_database_url_here')
print('✅ Connection successful!')
conn.close()
"
```

### SSL Issues
Railway requires SSL. The app is already configured for this.

### Migration Issues
If you have existing data, export it first:
```bash
# Export from old database
pg_dump old_database_url > backup.sql

# Import to Railway
psql new_railway_url < backup.sql
```

## 📞 Support

- Railway Docs: [docs.railway.app](https://docs.railway.app)
- Railway Discord: [discord.gg/railway](https://discord.gg/railway)

---

**Ready to proceed? Just provide your Railway DATABASE_URL and I'll help you complete the setup!**
