"""Change OAuthToken model to use user_id as primary key

Revision ID: 9bf8379bcaff
Revises: 78c3ec8ab3ad
Create Date: 2025-04-27 09:32:12.093010

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9bf8379bcaff'
down_revision = '78c3ec8ab3ad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('o_auth_token', schema=None) as batch_op:
        batch_op.drop_column('id')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('o_auth_token', schema=None) as batch_op:
        batch_op.add_column(sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False))

    # ### end Alembic commands ###
