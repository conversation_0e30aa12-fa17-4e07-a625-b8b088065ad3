# ======================================================
# DEVELOPMENT ENVIRONMENT CONFIGURATION
# ======================================================
# Copy this file to .env and fill in your values for local development
# In production (Render), these values are set in the dashboard

# Environment Settings
# -------------------
# This will be automatically set to 'production' on Render
FLASK_ENV=development
# Enable debug mode for development (disable in production)
FLASK_DEBUG=True
# Base URL for development (will be overridden in production)
BASE_URL=http://localhost:5000

# Security Settings
# ----------------
# Generate a strong random key for development
# In production, use a different, more secure key
FLASK_SECRET_KEY=your-secret-key-here
# Disable HTTPS for local development
HTTPS_ENABLED=False

# API Credentials
# --------------
# Google OAuth credentials (required for Gmail integration)
GOOGLE_CLIENT_ID=your-client-id-here
GOOGLE_CLIENT_SECRET=your-client-secret-here
# OpenRouter API key (required for AI responses)
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Database Configuration
# ---------------------
# For local development with Render PostgreSQL (external URL)
DATABASE_URL=postgresql://emailagent_user:<EMAIL>/emailagent
# Alternative: Internal URL for local development
# DATABASE_URL=*************************************************************************************
# Alternative: SQLite for simple local testing
# DATABASE_URL=sqlite:///app.db

# ======================================================
# NOTES:
# - This file is only used for local development
# - In production, environment variables are set in the Render dashboard
# - The application will automatically detect the environment and use the appropriate settings
# ======================================================
