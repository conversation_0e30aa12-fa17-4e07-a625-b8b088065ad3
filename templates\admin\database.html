{% extends 'base.html' %} {% block title %}Database Viewer{% endblock %} {%
block styles %} {{ super() }}
<style>
  .table-container {
    overflow-x: scroll !important; /* Force horizontal scrolling */
    max-height: 600px;
    overflow-y: auto;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: block !important; /* Force block display */
    position: relative; /* For positioning */
  }

  /* Custom scrollbar styling */
  .table-container::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  .table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .table-container::-webkit-scrollbar-thumb {
    background: #2daa9e;
    border-radius: 4px;
  }

  .table-container::-webkit-scrollbar-thumb:hover {
    background: #1e8a7e;
  }

  /* Table styling */
  .table {
    table-layout: auto !important;
    width: auto !important; /* Allow table to expand beyond container */
    min-width: 100%;
    white-space: nowrap; /* Prevent text wrapping */
  }
  .table th {
    position: sticky;
    top: 0;
    background-color: #2daa9e;
    color: white;
    z-index: 1;
    white-space: nowrap;
    padding: 10px;
    border: 1px solid #ccc;
  }
  .table td {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap !important;
    padding: 8px;
    border: 1px solid #ddd;
    vertical-align: top;
  }
  .table td:hover {
    white-space: normal;
    word-break: break-word;
    background-color: #f5f5f5;
    cursor: pointer;
    position: relative;
    z-index: 2;
  }

  /* Force table to be horizontally scrollable */
  @media screen and (max-width: 1200px) {
    .table-container {
      overflow-x: scroll !important;
    }
    .table {
      width: auto !important;
    }
  }
  .table tr:nth-child(even) {
    background-color: #f9f9f9;
  }
  .table tr:hover {
    background-color: #f0f0f0;
  }
  .sidebar {
    height: calc(100vh - 150px);
    overflow-y: auto;
  }
  .query-editor {
    font-family: monospace;
    min-height: 100px;
  }
  .loading {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
  }
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
  }
  .table-info {
    font-size: 0.9rem;
    color: #6c757d;
  }
</style>
{% endblock %} {% block content %}
<div class="container-fluid mt-4">
  <div class="row">
    <div class="col-md-3">
      <div class="card shadow-sm sidebar">
        <div
          class="card-header"
          style="background-color: #2daa9e; color: white"
        >
          <h5 class="mb-0">Database Tables</h5>
        </div>
        <div class="card-body p-0">
          <div class="list-group list-group-flush" id="tableList">
            <div class="text-center p-3">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2">Loading tables...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-9">
      <div class="card shadow-sm mb-4">
        <div
          class="card-header d-flex justify-content-between align-items-center"
          style="background-color: #2daa9e; color: white"
        >
          <h5 class="mb-0" id="tableTitle">Database Viewer</h5>
          <div>
            <button
              class="btn btn-sm btn-light"
              id="refreshBtn"
              style="display: none"
            >
              <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <a
              href="{{ url_for('admin_dashboard') }}"
              class="btn btn-sm btn-light"
            >
              <i class="fas fa-arrow-left"></i> Back to Admin
            </a>
          </div>
        </div>
        <div class="card-body">
          <div id="welcomeMessage">
            <div class="text-center py-5">
              <i class="fas fa-database fa-4x mb-3" style="color: #2daa9e"></i>
              <h4>Database Viewer</h4>
              <p class="lead">
                Select a table from the sidebar to view its data
              </p>
              <p>Or use the SQL query editor below to run custom queries</p>
            </div>
          </div>
          <div id="tableView" style="display: none">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <div class="table-info">
                Showing <span id="recordCount">0</span> of
                <span id="totalRecords">0</span> records
              </div>
              <div class="form-inline">
                <label for="limitSelect" class="mr-2">Limit:</label>
                <select
                  id="limitSelect"
                  class="form-select form-select-sm"
                  style="width: 100px"
                >
                  <option value="10">10</option>
                  <option value="50">50</option>
                  <option value="100" selected>100</option>
                  <option value="500">500</option>
                  <option value="1000">1000</option>
                </select>
              </div>
            </div>
            <div
              class="table-container"
              style="
                overflow-x: scroll !important;
                display: block !important;
                width: 100%;
              "
            >
              <table
                class="table table-striped table-bordered"
                id="dataTable"
                style="width: auto !important; min-width: 100%"
              >
                <thead>
                  <tr id="tableHeader"></tr>
                </thead>
                <tbody id="tableBody"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <div class="card shadow-sm">
        <div
          class="card-header"
          style="background-color: #2daa9e; color: white"
        >
          <h5 class="mb-0">SQL Query Editor</h5>
        </div>
        <div class="card-body">
          <div class="form-group mb-3">
            <textarea
              id="queryEditor"
              class="form-control query-editor"
              placeholder="Enter SQL query here..."
            ></textarea>
          </div>
          <div class="d-flex justify-content-between">
            <div>
              <button
                id="runQueryBtn"
                class="btn"
                style="background-color: #2daa9e; color: white"
              >
                <i class="fas fa-play"></i> Run Query
              </button>
              <button id="clearQueryBtn" class="btn btn-secondary">
                <i class="fas fa-eraser"></i> Clear
              </button>
            </div>
            <div class="text-muted small">
              <i class="fas fa-info-circle"></i>
              Destructive operations (DROP, DELETE, etc.) are disabled in
              production
            </div>
          </div>
          <div id="queryResult" class="mt-3" style="display: none">
            <div class="alert alert-info" id="queryMessage"></div>
            <div
              class="table-container"
              id="queryResultTable"
              style="display: none; overflow-x: scroll !important; width: 100%"
            >
              <table
                class="table table-striped table-bordered"
                style="width: auto !important; min-width: 100%"
              >
                <thead>
                  <tr id="queryResultHeader"></tr>
                </thead>
                <tbody id="queryResultBody"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading spinner -->
<div class="loading-overlay" id="loadingOverlay"></div>
<div class="loading" id="loadingSpinner">
  <div
    class="spinner-border text-light"
    style="width: 3rem; height: 3rem"
    role="status"
  >
    <span class="visually-hidden">Loading...</span>
  </div>
</div>
{% endblock %} {% block scripts %} {{ super() }}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Load tables
    loadTables();

    // Force horizontal scrolling to be enabled
    const tableContainers = document.querySelectorAll(".table-container");
    tableContainers.forEach((container) => {
      container.style.overflowX = "scroll";
      const table = container.querySelector("table");
      if (table) {
        table.style.width = "auto";
        table.style.minWidth = "100%";
      }
    });

    // Event listeners
    document
      .getElementById("refreshBtn")
      .addEventListener("click", function () {
        const currentTable = document.querySelector(".list-group-item.active");
        if (currentTable) {
          loadTableData(currentTable.dataset.table);
        }
      });

    document
      .getElementById("limitSelect")
      .addEventListener("change", function () {
        const currentTable = document.querySelector(".list-group-item.active");
        if (currentTable) {
          loadTableData(currentTable.dataset.table);
        }
      });

    document
      .getElementById("runQueryBtn")
      .addEventListener("click", function () {
        runQuery();
      });

    document
      .getElementById("clearQueryBtn")
      .addEventListener("click", function () {
        document.getElementById("queryEditor").value = "";
        document.getElementById("queryResult").style.display = "none";
      });

    // Add resize observer to ensure tables remain scrollable
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          const tableContainers =
            entry.target.querySelectorAll(".table-container");
          tableContainers.forEach((container) => {
            container.style.overflowX = "scroll";
          });
        }
      });

      resizeObserver.observe(document.body);
    }
  });

  function showLoading() {
    document.getElementById("loadingOverlay").style.display = "block";
    document.getElementById("loadingSpinner").style.display = "block";
  }

  function hideLoading() {
    document.getElementById("loadingOverlay").style.display = "none";
    document.getElementById("loadingSpinner").style.display = "none";
  }

  function loadTables() {
    showLoading();
    fetch("/admin/api/tables")
      .then((response) => response.json())
      .then((data) => {
        const tableList = document.getElementById("tableList");
        tableList.innerHTML = "";

        if (data.error) {
          tableList.innerHTML = `<div class="alert alert-danger m-3">${data.error}</div>`;
          return;
        }

        if (data.tables.length === 0) {
          tableList.innerHTML =
            '<div class="alert alert-info m-3">No tables found</div>';
          return;
        }

        data.tables.forEach((table) => {
          const item = document.createElement("a");
          item.href = "#";
          item.className = "list-group-item list-group-item-action";
          item.dataset.table = table;
          item.innerHTML = `<i class="fas fa-table mr-2"></i> ${table}`;
          item.addEventListener("click", function (e) {
            e.preventDefault();
            document.querySelectorAll(".list-group-item").forEach((el) => {
              el.classList.remove("active");
            });
            this.classList.add("active");
            loadTableData(table);
          });
          tableList.appendChild(item);
        });
      })
      .catch((error) => {
        console.error("Error loading tables:", error);
        document.getElementById(
          "tableList"
        ).innerHTML = `<div class="alert alert-danger m-3">Error loading tables: ${error.message}</div>`;
      })
      .finally(() => {
        hideLoading();
      });
  }

  function loadTableData(tableName) {
    showLoading();
    const limit = document.getElementById("limitSelect").value;

    document.getElementById("welcomeMessage").style.display = "none";
    document.getElementById("tableView").style.display = "block";
    document.getElementById("refreshBtn").style.display = "inline-block";
    document.getElementById("tableTitle").textContent = `Table: ${tableName}`;

    // Ensure table container has horizontal scrolling
    const tableContainer = document.querySelector(".table-container");
    if (tableContainer) {
      tableContainer.style.overflowX = "scroll";
      tableContainer.style.display = "block";
    }

    fetch(`/admin/api/table/${tableName}?limit=${limit}`)
      .then((response) => response.json())
      .then((data) => {
        if (data.error) {
          alert(`Error: ${data.error}`);
          return;
        }

        // Update record count
        document.getElementById("recordCount").textContent = data.count;
        document.getElementById("totalRecords").textContent = data.total;

        // Clear existing table
        const tableHeader = document.getElementById("tableHeader");
        const tableBody = document.getElementById("tableBody");
        tableHeader.innerHTML = "";
        tableBody.innerHTML = "";

        // Add headers
        data.columns.forEach((column) => {
          const th = document.createElement("th");
          th.textContent = column.name;
          th.title = column.type;
          tableHeader.appendChild(th);
        });

        // Add data rows
        data.data.forEach((row) => {
          const tr = document.createElement("tr");
          data.columns.forEach((column) => {
            const td = document.createElement("td");
            const value = row[column.name];

            // Format the value based on type
            if (value === null) {
              td.innerHTML = '<em class="text-muted">NULL</em>';
            } else if (typeof value === "object") {
              td.textContent = JSON.stringify(value);
              td.title = JSON.stringify(value);
            } else if (typeof value === "string" && value.length > 100) {
              td.textContent = value.substring(0, 100) + "...";
              td.title = value;
            } else {
              td.textContent = value;
              if (value && typeof value === "string") {
                td.title = value;
              }
            }

            tr.appendChild(td);
          });
          tableBody.appendChild(tr);
        });
      })
      .catch((error) => {
        console.error("Error loading table data:", error);
        alert(`Error loading table data: ${error.message}`);
      })
      .finally(() => {
        hideLoading();
      });
  }

  function runQuery() {
    const query = document.getElementById("queryEditor").value.trim();
    if (!query) {
      alert("Please enter a SQL query");
      return;
    }

    showLoading();
    fetch("/admin/api/query", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ query: query }),
    })
      .then((response) => response.json())
      .then((data) => {
        const queryResult = document.getElementById("queryResult");
        const queryMessage = document.getElementById("queryMessage");
        const queryResultTable = document.getElementById("queryResultTable");
        const queryResultHeader = document.getElementById("queryResultHeader");
        const queryResultBody = document.getElementById("queryResultBody");

        queryResult.style.display = "block";

        if (data.error) {
          queryMessage.className = "alert alert-danger";
          queryMessage.textContent = `Error: ${data.error}`;
          queryResultTable.style.display = "none";
          return;
        }

        queryMessage.className = "alert alert-success";
        queryMessage.textContent = data.message;

        if (data.columns && data.data) {
          queryResultTable.style.display = "block";
          queryResultTable.style.overflowX = "scroll";

          // Ensure the table has proper styling for horizontal scrolling
          const resultTable = queryResultTable.querySelector("table");
          if (resultTable) {
            resultTable.style.width = "auto";
            resultTable.style.minWidth = "100%";
          }

          // Clear existing table
          queryResultHeader.innerHTML = "";
          queryResultBody.innerHTML = "";

          // Add headers
          data.columns.forEach((column) => {
            const th = document.createElement("th");
            th.textContent = column.name;
            queryResultHeader.appendChild(th);
          });

          // Add data rows
          data.data.forEach((row) => {
            const tr = document.createElement("tr");
            data.columns.forEach((column) => {
              const td = document.createElement("td");
              const value = row[column.name];

              // Format the value based on type
              if (value === null) {
                td.innerHTML = '<em class="text-muted">NULL</em>';
              } else if (typeof value === "object") {
                td.textContent = JSON.stringify(value);
                td.title = JSON.stringify(value);
              } else if (typeof value === "string" && value.length > 100) {
                td.textContent = value.substring(0, 100) + "...";
                td.title = value;
              } else {
                td.textContent = value;
                if (value && typeof value === "string") {
                  td.title = value;
                }
              }

              tr.appendChild(td);
            });
            queryResultBody.appendChild(tr);
          });
        } else {
          queryResultTable.style.display = "none";
        }
      })
      .catch((error) => {
        console.error("Error executing query:", error);
        const queryResult = document.getElementById("queryResult");
        const queryMessage = document.getElementById("queryMessage");

        queryResult.style.display = "block";
        queryMessage.className = "alert alert-danger";
        queryMessage.textContent = `Error executing query: ${error.message}`;
        document.getElementById("queryResultTable").style.display = "none";
      })
      .finally(() => {
        hideLoading();
      });
  }
</script>
{% endblock %}
