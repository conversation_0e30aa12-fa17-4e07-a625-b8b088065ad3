"""
Migration script to add is_admin column to User model.
"""
import sys
import os

# Add the parent directory to the path so we can import app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app, db
from sqlalchemy import text

def add_admin_role():
    """Add is_admin column to User model"""
    print("Adding is_admin column to User model...")

    with app.app_context():
        try:
            # Check if the column already exists
            result = db.session.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'user'
                AND column_name = 'is_admin'
            """))

            if result.fetchone():
                print("Column 'is_admin' already exists in User model")
                return True

            # Add the column
            db.session.execute(text("""
                ALTER TABLE "user"
                ADD COLUMN is_admin BOOLEAN NOT NULL DEFAULT FALSE
            """))

            db.session.commit()
            print("Column 'is_admin' added to User model")
            return True

        except Exception as e:
            print(f"Error adding is_admin column: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    success = add_admin_role()
    if not success:
        import sys
        sys.exit(1)
