#!/usr/bin/env python3
"""
Railway Database Initialization Script
=====================================

This script initializes the Railway PostgreSQL database with the correct schema.
Run this after setting up your Railway database.

Usage:
1. Set your Railway DATABASE_URL in .env file
2. Run: python init_railway_db.py
"""

import os
from dotenv import load_dotenv
from app import app, db
from models import User, OAuthToken, UserSettings, UserLog, AutomatedEmail, EmailReply, UserStats

# Load environment variables
load_dotenv()

def init_database():
    """Initialize the Railway database with all tables"""
    print("🚀 Initializing Railway PostgreSQL Database...")
    
    # Check if DATABASE_URL is set
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ ERROR: DATABASE_URL not found in environment variables")
        print("Please set your Railway database URL in the .env file")
        return False
    
    if database_url.startswith('postgresql://'):
        print(f"✅ Using PostgreSQL database: {database_url.split('@')[1].split('/')[0]}")
    else:
        print("⚠️  WARNING: DATABASE_URL doesn't appear to be PostgreSQL")
    
    try:
        with app.app_context():
            print("📊 Creating database tables...")
            
            # Drop all tables (fresh start)
            print("🗑️  Dropping existing tables...")
            db.drop_all()
            
            # Create all tables
            print("🏗️  Creating new tables...")
            db.create_all()
            
            # Verify tables were created
            from sqlalchemy import text
            result = db.session.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result.fetchall()]
            
            print(f"✅ Successfully created {len(tables)} tables:")
            for table in tables:
                print(f"   - {table}")
            
            # Test database connection
            print("🔍 Testing database connection...")
            db.session.execute(text('SELECT 1'))
            db.session.commit()
            print("✅ Database connection test successful!")
            
            print("\n🎉 Railway database initialization completed successfully!")
            print("\n📝 Next steps:")
            print("1. Update your Render environment variables with the Railway DATABASE_URL")
            print("2. Deploy your application")
            print("3. Your app will now use Railway PostgreSQL!")
            
            return True
            
    except Exception as e:
        print(f"❌ ERROR during database initialization: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check your Railway DATABASE_URL is correct")
        print("2. Ensure your Railway database is running")
        print("3. Check network connectivity")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚂 RAILWAY DATABASE INITIALIZATION")
    print("=" * 60)
    
    success = init_database()
    
    if success:
        print("\n✅ All done! Your Railway database is ready to use.")
    else:
        print("\n❌ Initialization failed. Please check the errors above.")
    
    print("=" * 60)
